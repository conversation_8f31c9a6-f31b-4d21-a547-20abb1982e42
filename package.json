{"name": "ledgerly-be", "private": true, "packageManager": "pnpm@9.0.0", "scripts": {"lint": "turbo run lint", "typecheck": "turbo run typecheck", "build": "turbo run build", "test": "turbo run test", "dev": "turbo run dev", "db:types": "supabase gen types typescript --linked > packages/types/src/supabase.ts", "db:migrate:local": "psql $DATABASE_URL -f packages/db/migrations/all.sql", "db:seed:local": "psql $DATABASE_URL -f packages/db/seeds/dev_seed.sql"}, "devDependencies": {"turbo": "^2.0.0"}}