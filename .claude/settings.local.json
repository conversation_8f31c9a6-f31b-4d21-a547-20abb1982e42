{"permissions": {"allow": ["Read(//Users/<USER>/Documents/Coding/wt/track-auth/apps/web/**)", "Read(//Users/<USER>/Documents/Coding/wt/track-auth/apps/web/**)", "Read(//Users/<USER>/Documents/Coding/wt/track-auth/packages/dal/src/**)", "Read(//Users/<USER>/Documents/Coding/wt/track-auth/packages/db/migrations/**)", "Bash(grep:*)", "Bash(npm test:*)", "Bash(npm run typecheck:*)", "Bash(npm run lint)", "Bash(pnpm -w typecheck)", "Bash(pnpm --filter @ledgerly/types lint)", "Bash(pnpm -w test)", "Bash(python -m pytest tests/ -v)", "Bash(pnpm --filter @ledgerly/dal test)", "Bash(pnpm typecheck:*)", "Bash(git checkout:*)"], "deny": [], "ask": [], "additionalDirectories": ["/Users/<USER>/Documents/Coding/wt/track-auth/apps/web", "/Users/<USER>/Documents/Coding/wt/track-auth", "/Users/<USER>/Documents/Coding/wt/track-inbox-e2e", "/Users/<USER>/Documents/Coding/wt/wt"]}}