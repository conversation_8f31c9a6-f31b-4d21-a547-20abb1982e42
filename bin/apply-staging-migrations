#!/bin/bash

# Apply Database Migrations to Staging
# This script applies all new migrations to the staging environment

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🗄️ Applying Database Migrations to Staging${NC}"
echo "=================================================="

# Check if STAGING_DATABASE_URL is set
if [ -z "${STAGING_DATABASE_URL:-}" ]; then
    echo -e "${RED}❌ STAGING_DATABASE_URL environment variable not set${NC}"
    echo ""
    echo "Please set the staging database URL:"
    echo "export STAGING_DATABASE_URL=\"postgresql://postgres:[PASSWORD]@db.kqkeqzpccirdcosiqusl.supabase.co:5432/postgres\""
    echo ""
    echo "Get the password from Supabase Dashboard → Settings → Database"
    exit 1
fi

echo -e "${GREEN}✅ Database URL configured${NC}"

# Test database connection
echo -e "${YELLOW}🔌 Testing database connection...${NC}"
if ! psql "$STAGING_DATABASE_URL" -c "SELECT version();" > /dev/null 2>&1; then
    echo -e "${RED}❌ Cannot connect to staging database${NC}"
    echo "Please check your database URL and credentials"
    exit 1
fi

echo -e "${GREEN}✅ Database connection successful${NC}"

# Apply migrations
echo -e "${YELLOW}📊 Applying migrations...${NC}"
psql "$STAGING_DATABASE_URL" -f packages/db/migrations/all.sql

echo -e "${GREEN}✅ All migrations applied successfully${NC}"

# Verify migrations
echo -e "${YELLOW}🔍 Verifying migrations...${NC}"

# Check for new tables
NEW_TABLES=$(psql "$STAGING_DATABASE_URL" -t -c "
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('pending_invites', 'security_events');
" | wc -l)

if [ "$NEW_TABLES" -eq 2 ]; then
    echo -e "${GREEN}✅ New tables created successfully${NC}"
else
    echo -e "${RED}❌ Expected 2 new tables, found $NEW_TABLES${NC}"
fi

# Check for new RPC functions
NEW_FUNCTIONS=$(psql "$STAGING_DATABASE_URL" -t -c "
SELECT count(*) FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name LIKE 'rpc_%';
")

echo -e "${GREEN}✅ Found $NEW_FUNCTIONS RPC functions${NC}"

echo -e "${BLUE}🎉 Migration verification complete!${NC}"
echo ""
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Generate types: pnpm db:types:staging"
echo "2. Run integration tests"
echo "3. Verify application functionality"
