export interface BankTransaction {
  transactionDate: string
  valueDate: string
  amount: number
  counterpartyName?: string
  counterpartyAccount?: string
  description: string
  reference?: string
  transactionId?: string
}

export interface BankStatementParser {
  format: string
  parse(content: string): BankTransaction[]
  validate(content: string): boolean
}

export class CsvBankParser implements BankStatementParser {
  format = 'csv'

  validate(content: string): boolean {
    const lines = content.split('\n').filter(line => line.trim())
    return lines.length > 1 && lines[0].includes(',')
  }

  parse(content: string): BankTransaction[] {
    const lines = content.split('\n').filter(line => line.trim())
    
    if (lines.length < 2) {
      throw new Error('CSV must have at least header and one data row')
    }

    const header = lines[0].split(',').map(h => h.trim().toLowerCase())
    const transactions: BankTransaction[] = []

    // Try to map common CSV formats
    const mappings = this.detectColumnMappings(header)

    for (let i = 1; i < lines.length; i++) {
      const values = this.parseCsvLine(lines[i])
      
      if (values.length < mappings.requiredColumns) {
        continue // Skip incomplete rows
      }

      try {
        const transaction = this.mapCsvRowToTransaction(values, mappings)
        if (transaction) {
          transactions.push(transaction)
        }
      } catch (error) {
        console.warn(`Skipping row ${i + 1}: ${error}`)
      }
    }

    return transactions
  }

  private detectColumnMappings(header: string[]): any {
    const mappings: any = {
      requiredColumns: 4,
      transactionDate: -1,
      valueDate: -1,
      amount: -1,
      description: -1,
      counterpartyName: -1,
      counterpartyAccount: -1,
      reference: -1
    }

    header.forEach((col, index) => {
      const normalized = col.replace(/['"]/g, '').toLowerCase()
      
      if (normalized.includes('date') && normalized.includes('transaction') || 
          normalized.includes('booking') || normalized === 'date') {
        mappings.transactionDate = index
      }
      
      if (normalized.includes('value') && normalized.includes('date')) {
        mappings.valueDate = index
      }
      
      if (normalized.includes('amount') || normalized.includes('bedrag') || 
          normalized === 'sum' || normalized === 'montant') {
        mappings.amount = index
      }
      
      if (normalized.includes('description') || normalized.includes('omschrijving') || 
          normalized.includes('communication') || normalized.includes('memo')) {
        mappings.description = index
      }
      
      if (normalized.includes('counterparty') || normalized.includes('beneficiary') || 
          normalized.includes('payee') || normalized.includes('tegenpartij')) {
        mappings.counterpartyName = index
      }
      
      if (normalized.includes('account') && normalized.includes('counterparty') || 
          normalized.includes('iban') || normalized.includes('rekening')) {
        mappings.counterpartyAccount = index
      }
      
      if (normalized.includes('reference') || normalized.includes('ref') || 
          normalized.includes('structured')) {
        mappings.reference = index
      }
    })

    // Set value date same as transaction date if not found
    if (mappings.valueDate === -1) {
      mappings.valueDate = mappings.transactionDate
    }

    return mappings
  }

  private parseCsvLine(line: string): string[] {
    // Simple CSV parser - handles basic quoted fields
    const values: string[] = []
    let current = ''
    let inQuotes = false
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i]
      
      if (char === '"' && (i === 0 || line[i - 1] === ',')) {
        inQuotes = true
      } else if (char === '"' && inQuotes && (i === line.length - 1 || line[i + 1] === ',')) {
        inQuotes = false
      } else if (char === ',' && !inQuotes) {
        values.push(current.trim())
        current = ''
      } else {
        current += char
      }
    }
    
    values.push(current.trim())
    return values
  }

  private mapCsvRowToTransaction(values: string[], mappings: any): BankTransaction | null {
    const transactionDate = values[mappings.transactionDate]?.trim()
    const valueDate = values[mappings.valueDate]?.trim()
    const amountStr = values[mappings.amount]?.trim()
    const description = values[mappings.description]?.trim()

    if (!transactionDate || !amountStr || !description) {
      return null
    }

    // Parse amount (handle various formats)
    let amount = parseFloat(amountStr.replace(/[^\d.,-]/g, '').replace(',', '.'))
    if (isNaN(amount)) {
      throw new Error(`Invalid amount: ${amountStr}`)
    }

    return {
      transactionDate: this.parseDate(transactionDate),
      valueDate: this.parseDate(valueDate || transactionDate),
      amount,
      description,
      counterpartyName: values[mappings.counterpartyName]?.trim() || undefined,
      counterpartyAccount: values[mappings.counterpartyAccount]?.trim() || undefined,
      reference: values[mappings.reference]?.trim() || undefined,
      transactionId: `csv-${Date.now()}-${Math.random()}`
    }
  }

  private parseDate(dateStr: string): string {
    // Try various date formats
    const formats = [
      /^(\d{4})-(\d{2})-(\d{2})$/,         // YYYY-MM-DD
      /^(\d{2})\/(\d{2})\/(\d{4})$/,       // DD/MM/YYYY
      /^(\d{2})-(\d{2})-(\d{4})$/,         // DD-MM-YYYY
      /^(\d{2})\.(\d{2})\.(\d{4})$/,       // DD.MM.YYYY
    ]

    for (const format of formats) {
      const match = dateStr.match(format)
      if (match) {
        if (format === formats[0]) {
          // Already in YYYY-MM-DD format
          return dateStr
        } else {
          // Convert DD/MM/YYYY to YYYY-MM-DD
          const day = match[1]
          const month = match[2]
          const year = match[3]
          return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`
        }
      }
    }

    throw new Error(`Unable to parse date: ${dateStr}`)
  }
}

export class CodaBankParser implements BankStatementParser {
  format = 'coda'

  validate(content: string): boolean {
    const lines = content.split('\n')
    return lines.length > 0 && lines[0].startsWith('0000')
  }

  parse(content: string): BankTransaction[] {
    // CODA format parser stub
    // This would implement the full CODA specification
    throw new Error('CODA parser not yet implemented')
  }
}

export class BankParserFactory {
  private static parsers: BankStatementParser[] = [
    new CsvBankParser(),
    new CodaBankParser()
  ]

  static detectFormat(content: string): string | null {
    for (const parser of this.parsers) {
      if (parser.validate(content)) {
        return parser.format
      }
    }
    return null
  }

  static getParser(format: string): BankStatementParser | null {
    return this.parsers.find(p => p.format === format) || null
  }

  static parse(content: string, format?: string): BankTransaction[] {
    const detectedFormat = format || this.detectFormat(content)
    
    if (!detectedFormat) {
      throw new Error('Unable to detect bank statement format')
    }

    const parser = this.getParser(detectedFormat)
    if (!parser) {
      throw new Error(`No parser available for format: ${detectedFormat}`)
    }

    return parser.parse(content)
  }
}