import { z } from 'zod'

// Journal Line contract
export const JournalLineSchema = z.object({
  journal_id: z.number(),
  account_id: z.number(),
  description: z.string().optional(),
  debit_amount: z.number().positive().optional(),
  credit_amount: z.number().positive().optional(),
  vat_code_id: z.number().optional()
}).refine(
  data => (data.debit_amount && !data.credit_amount) || (!data.debit_amount && data.credit_amount),
  {
    message: "Either debit_amount or credit_amount must be provided, but not both"
  }
)

export type JournalLine = z.infer<typeof JournalLineSchema>

// Post Journal contract
export const PostJournalSchema = z.object({
  entity_id: z.number(),
  journal_type: z.string().default('general'),
  reference: z.string().optional(),
  description: z.string().min(1),
  transaction_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  lines: z.array(JournalLineSchema).min(2)
}).refine(
  data => {
    const totalDebits = data.lines
      .filter(line => line.debit_amount)
      .reduce((sum, line) => sum + (line.debit_amount || 0), 0)
    
    const totalCredits = data.lines
      .filter(line => line.credit_amount)
      .reduce((sum, line) => sum + (line.credit_amount || 0), 0)
    
    return Math.abs(totalDebits - totalCredits) < 0.01
  },
  {
    message: "Journal must be balanced: sum of debits must equal sum of credits"
  }
)

export type PostJournal = z.infer<typeof PostJournalSchema>

// Invoice Create contract
export const InvoiceCreateSchema = z.object({
  entity_id: z.number(),
  kind: z.enum(['sale', 'purchase']),
  number: z.string().min(1),
  counterparty_name: z.string().min(1),
  counterparty_vat: z.string().optional(),
  invoice_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  due_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  total_amount: z.number().positive(),
  vat_amount: z.number().min(0).default(0),
  lines: z.array(z.object({
    description: z.string().min(1),
    quantity: z.number().positive(),
    unit_price: z.number().positive(),
    vat_code_id: z.number(),
    total: z.number().positive()
  })).min(1)
})

export type InvoiceCreate = z.infer<typeof InvoiceCreateSchema>

// Bank Import contract
export const BankImportSchema = z.object({
  entity_id: z.number(),
  bank_account_id: z.number(),
  format: z.enum(['csv', 'coda', 'mt940']),
  transactions: z.array(z.object({
    transaction_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
    value_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
    amount: z.number(),
    counterparty_name: z.string().optional(),
    counterparty_account: z.string().optional(),
    description: z.string().min(1),
    reference: z.string().optional(),
    transaction_id: z.string().optional()
  })).min(1)
})

export type BankImport = z.infer<typeof BankImportSchema>

// VAT Export contract
export const VatExportSchema = z.object({
  entity_id: z.number(),
  period_start: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  period_end: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  format: z.enum(['xml', 'csv', 'xlsx']),
  include_zero_amounts: z.boolean().default(false)
})

export type VatExport = z.infer<typeof VatExportSchema>

// Operating Mode Switch contract
export const SwitchModeSchema = z.object({
  entity_id: z.number(),
  mode: z.enum(['ledger', 'assist']),
  config: z.record(z.unknown()).default({})
})

export type SwitchMode = z.infer<typeof SwitchModeSchema>

// Entity Role Grant contract
export const GrantEntityRoleSchema = z.object({
  entity_id: z.number(),
  user_id: z.string().uuid(),
  role: z.enum(['owner', 'admin', 'accountant', 'bookkeeper', 'viewer'])
})

export type GrantEntityRole = z.infer<typeof GrantEntityRoleSchema>

// Common response types
export const ApiResponseSchema = z.object({
  success: z.boolean(),
  data: z.unknown().optional(),
  error: z.string().optional()
})

export type ApiResponse = z.infer<typeof ApiResponseSchema>

export const PaginationSchema = z.object({
  page: z.number().positive().default(1),
  limit: z.number().positive().max(100).default(20),
  total: z.number().min(0).optional(),
  pages: z.number().min(0).optional()
})

export type Pagination = z.infer<typeof PaginationSchema>

// =============================================================================
// INBOX E2E - Document Processing Contracts
// =============================================================================

// VAT Rate enum for Belgian tax system
export const VatRate = z.union([
  z.literal(21), // Standard rate
  z.literal(12), // Reduced rate
  z.literal(6),  // Low rate
  z.literal(0)   // Zero rate
])
export type VatRate = z.infer<typeof VatRate>

// AI Extraction Result - what comes back from the AI processing
export const ExtractionResult = z.object({
  supplier: z.object({
    name: z.string().min(1),
    vat: z.string().optional(),        // "BE0123456789" format
    iban: z.string().optional(),       // Optional bank account
    address: z.string().optional()     // Optional address
  }),
  invoice: z.object({
    number: z.string().min(1),
    issueDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
    dueDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
    currency: z.literal("EUR"),        // Only EUR supported for now
    net: z.string(),                   // Decimal as string for precision
    vat: z.string(),                   // Total VAT amount as string
    gross: z.string()                  // Total gross amount as string
  }),
  lines: z.array(z.object({
    description: z.string().min(1),
    quantity: z.string().default("1"), // Quantity as decimal string
    unitPrice: z.string(),             // Unit price as decimal string  
    vatRate: VatRate,                  // VAT rate percentage
    accountHint: z.number().optional() // Suggested account_id from templates
  })).min(1),
  confidence: z.number().min(0).max(1).optional() // Overall extraction confidence
})
export type ExtractionResult = z.infer<typeof ExtractionResult>

// Journal line suggestion - balanced lines ready for posting
export const SuggestionLine = z.object({
  accountId: z.number(),             // GL account (expense, VAT, payable)
  debit: z.string().default("0"),   // Debit amount as decimal string
  credit: z.string().default("0"),  // Credit amount as decimal string
  vatCodeId: z.number().optional(), // VAT code for this line
  memo: z.string().optional()       // Description/memo for the line
})
export type SuggestionLine = z.infer<typeof SuggestionLine>

// Full suggestion with balanced journal lines
export const Suggestion = z.object({
  journalDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  reference: z.string().optional(),  // Invoice number or reference
  description: z.string().min(1),    // Journal description
  lines: z.array(SuggestionLine).min(2) // At least expense + payable
}).refine(
  data => {
    // Ensure the journal is balanced
    const totalDebits = data.lines.reduce((sum, line) => 
      sum + parseFloat(line.debit || '0'), 0)
    const totalCredits = data.lines.reduce((sum, line) => 
      sum + parseFloat(line.credit || '0'), 0)
    return Math.abs(totalDebits - totalCredits) < 0.01
  },
  {
    message: "Suggestion must be balanced: sum of debits must equal sum of credits"
  }
)
export type Suggestion = z.infer<typeof Suggestion>

// Document upload request
export const DocumentUploadRequest = z.object({
  entity_id: z.number(),
  path: z.string().min(1),           // Storage path where file was uploaded
  mime_type: z.string().min(1),      // MIME type of uploaded file
  source: z.enum(['upload', 'email', 'api']).default('upload')
})
export type DocumentUploadRequest = z.infer<typeof DocumentUploadRequest>

// Document processing response
export const DocumentUploadResponse = z.object({
  document_id: z.number(),
  status: z.literal('uploaded'),
  message: z.string().optional()
})
export type DocumentUploadResponse = z.infer<typeof DocumentUploadResponse>

// Document status enum
export const DocumentStatus = z.enum([
  'uploaded',    // File uploaded, not yet processed
  'extracted',   // AI extraction completed
  'suggested',   // Journal lines suggested  
  'confirmed',   // User confirmed, ready to post/export
  'posted',      // Posted to journal (ledger mode)
  'exported',    // Exported for integration (assist mode)
  'failed'       // Processing failed
])
export type DocumentStatus = z.infer<typeof DocumentStatus>

// Document list item for UI
export const DocumentListItem = z.object({
  id: z.number(),
  entity_id: z.number(),
  path: z.string(),
  mime_type: z.string(),
  source: z.string(),
  status: DocumentStatus,
  confidence: z.number().min(0).max(1).optional(),
  created_at: z.string(), // ISO datetime string
  updated_at: z.string(),
  // Extracted info for display (if available)
  supplier_name: z.string().optional(),
  invoice_number: z.string().optional(),
  invoice_date: z.string().optional(),
  gross_amount: z.string().optional(),
  error_msg: z.string().optional()
})
export type DocumentListItem = z.infer<typeof DocumentListItem>

// Document list response with pagination
export const DocumentListResponse = z.object({
  documents: z.array(DocumentListItem),
  pagination: PaginationSchema
})
export type DocumentListResponse = z.infer<typeof DocumentListResponse>

// Document detail with full extraction and suggestion
export const DocumentDetail = z.object({
  id: z.number(),
  entity_id: z.number(),
  path: z.string(),
  status: DocumentStatus,
  extraction: ExtractionResult.optional(),
  suggestion: Suggestion.optional(),
  confidence: z.number().min(0).max(1).optional(),
  posted_journal_id: z.number().optional(),
  export_ref: z.string().optional(),
  error_msg: z.string().optional(),
  created_at: z.string(),
  updated_at: z.string()
})
export type DocumentDetail = z.infer<typeof DocumentDetail>

// User confirmation request - may include corrections to extraction
export const ConfirmRequest = z.object({
  document_id: z.number(),
  correction: ExtractionResult.optional() // If user edits fields before confirm
})
export type ConfirmRequest = z.infer<typeof ConfirmRequest>

// Confirmation response
export const ConfirmResponse = z.object({
  document_id: z.number(),
  status: z.enum(['posted', 'exported']),
  journal_id: z.number().optional(),    // Set if posted to journal (ledger mode)
  export_ref: z.string().optional(),    // Set if exported (assist mode)
  message: z.string().optional()
})
export type ConfirmResponse = z.infer<typeof ConfirmResponse>

// Feature flag check
export const FeatureFlagRequest = z.object({
  entity_id: z.number(),
  flag: z.string().min(1)
})
export type FeatureFlagRequest = z.infer<typeof FeatureFlagRequest>

export const FeatureFlagResponse = z.object({
  enabled: z.boolean(),
  config: z.record(z.unknown()).default({})
})
export type FeatureFlagResponse = z.infer<typeof FeatureFlagResponse>

// Workers processing request (internal API)
export const ProcessDocumentRequest = z.object({
  document_id: z.number(),
  entity_id: z.number(),
  file_url: z.string().url()           // Signed URL to download file
})
export type ProcessDocumentRequest = z.infer<typeof ProcessDocumentRequest>

// Export canonical format for Assist mode integrations
export const ExportPayload = z.object({
  entity_id: z.number(),
  document_id: z.number(),
  supplier: z.object({
    name: z.string(),
    vat: z.string().optional(),
    iban: z.string().optional()
  }),
  lines: z.array(z.object({
    account_code: z.string(),          // Account code for integration
    description: z.string(),
    net: z.string(),                   // Net amount as decimal string
    vat_rate: VatRate,                 // VAT rate percentage
    vat_amount: z.string()             // VAT amount as decimal string
  })).min(1),
  total_gross: z.string(),             // Total gross amount
  invoice_number: z.string(),
  issue_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  due_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  currency: z.literal("EUR")
})
export type ExportPayload = z.infer<typeof ExportPayload>