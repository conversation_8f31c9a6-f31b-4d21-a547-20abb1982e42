[sqlfluff]
dialect = postgres
templater = jinja
sql_file_exts = .sql,.psql

[sqlfluff:indentation]
indented_joins = False
indented_ctes = False
indented_using_on = True
template_blocks_indent = True

[sqlfluff:layout:type:comma]
line_position = leading

[sqlfluff:rules]
tab_space_size = 2
max_line_length = 120
indent_unit = space
comma_style = leading
aliasing = explicit

[sqlfluff:rules:L010]
capitalisation_policy = upper

[sqlfluff:rules:L014]
unquoted_identifiers_policy = lower

[sqlfluff:rules:L030]
capitalisation_policy = upper