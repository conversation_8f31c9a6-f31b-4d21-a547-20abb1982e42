-- SQL RPC Functions for atomic operations
-- These functions provide safe, atomic operations with proper validation

-- Function: Post a journal entry
CREATE OR REPLACE FUNCTION rpc_post_journal(
  p_entity bigint,
  p_type text,
  p_reference text DEFAULT NULL,
  p_description text,
  p_date date,
  p_lines jsonb
) RETURNS bigint
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_journal_id bigint;
  v_line jsonb;
  v_total_debits numeric := 0;
  v_total_credits numeric := 0;
  v_account_exists boolean;
BEGIN
  -- Validate entity access (RLS will handle this but double-check)
  IF NOT EXISTS (
    SELECT 1 FROM entities e 
    WHERE e.id = p_entity 
    AND e.id IN (SELECT entity_id FROM v_user_entities)
  ) THEN
    RAISE EXCEPTION 'Access denied to entity %', p_entity;
  END IF;
  
  -- Validate that we have at least 2 lines
  IF jsonb_array_length(p_lines) < 2 THEN
    RAISE EXCEPTION 'Journal must have at least 2 lines';
  END IF;
  
  -- Validate all accounts exist and belong to the entity
  FOR v_line IN SELECT * FROM jsonb_array_elements(p_lines)
  LOOP
    SELECT EXISTS (
      SELECT 1 FROM accounts a 
      WHERE a.id = (v_line->>'account_id')::bigint 
      AND a.entity_id = p_entity
      AND a.is_active = true
    ) INTO v_account_exists;
    
    IF NOT v_account_exists THEN
      RAISE EXCEPTION 'Account % does not exist or is not active for entity %', 
        v_line->>'account_id', p_entity;
    END IF;
    
    -- Accumulate totals for balance check
    IF v_line->>'debit_amount' IS NOT NULL THEN
      v_total_debits := v_total_debits + (v_line->>'debit_amount')::numeric;
    END IF;
    
    IF v_line->>'credit_amount' IS NOT NULL THEN
      v_total_credits := v_total_credits + (v_line->>'credit_amount')::numeric;
    END IF;
  END LOOP;
  
  -- Check balance before we even start
  IF ABS(v_total_debits - v_total_credits) > 0.001 THEN
    RAISE EXCEPTION 'Journal is not balanced: debits=% credits=%', 
      v_total_debits, v_total_credits;
  END IF;
  
  -- Create the journal
  INSERT INTO journals (entity_id, journal_type, reference, description, transaction_date, created_by)
  VALUES (p_entity, p_type, p_reference, p_description, p_date, auth.uid())
  RETURNING id INTO v_journal_id;
  
  -- Insert journal lines
  FOR v_line IN SELECT * FROM jsonb_array_elements(p_lines)
  LOOP
    INSERT INTO journal_lines (
      journal_id, 
      account_id, 
      description, 
      debit_amount, 
      credit_amount,
      vat_code_id
    ) VALUES (
      v_journal_id,
      (v_line->>'account_id')::bigint,
      v_line->>'description',
      CASE WHEN v_line->>'debit_amount' IS NOT NULL 
           THEN (v_line->>'debit_amount')::numeric 
           ELSE NULL END,
      CASE WHEN v_line->>'credit_amount' IS NOT NULL 
           THEN (v_line->>'credit_amount')::numeric 
           ELSE NULL END,
      CASE WHEN v_line->>'vat_code_id' IS NOT NULL 
           THEN (v_line->>'vat_code_id')::bigint 
           ELSE NULL END
    );
  END LOOP;
  
  RETURN v_journal_id;
END;
$$;

-- Function: Switch operating mode
CREATE OR REPLACE FUNCTION rpc_switch_mode(
  p_entity bigint,
  p_mode text,
  p_config jsonb DEFAULT '{}'::jsonb
) RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Validate entity access and role
  IF NOT EXISTS (
    SELECT 1 FROM v_user_entities vue
    WHERE vue.entity_id = p_entity 
    AND vue.role IN ('owner', 'admin')
  ) THEN
    RAISE EXCEPTION 'Access denied or insufficient permissions for entity %', p_entity;
  END IF;
  
  -- Validate mode
  IF p_mode NOT IN ('ledger', 'assist') THEN
    RAISE EXCEPTION 'Invalid mode: %. Must be ''ledger'' or ''assist''', p_mode;
  END IF;
  
  -- Update or insert operating mode
  INSERT INTO operating_modes (entity_id, mode, config)
  VALUES (p_entity, p_mode, p_config)
  ON CONFLICT (entity_id) 
  DO UPDATE SET 
    mode = EXCLUDED.mode,
    config = EXCLUDED.config,
    created_at = NOW();
    
  -- Log the mode switch
  INSERT INTO audit_events (entity_id, table_name, record_id, operation, new_values, user_id)
  VALUES (
    p_entity, 
    'operating_modes', 
    p_entity, 
    'UPDATE',
    jsonb_build_object('mode', p_mode, 'config', p_config),
    auth.uid()
  );
END;
$$;

-- Function: Grant entity role
CREATE OR REPLACE FUNCTION rpc_grant_entity_role(
  p_entity bigint,
  p_user uuid,
  p_role text
) RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Validate current user has admin rights
  IF NOT EXISTS (
    SELECT 1 FROM v_user_entities vue
    WHERE vue.entity_id = p_entity 
    AND vue.user_id = auth.uid()
    AND vue.role IN ('owner', 'admin')
  ) THEN
    RAISE EXCEPTION 'Access denied or insufficient permissions for entity %', p_entity;
  END IF;
  
  -- Validate role
  IF p_role NOT IN ('owner', 'admin', 'accountant', 'bookkeeper', 'viewer') THEN
    RAISE EXCEPTION 'Invalid role: %', p_role;
  END IF;
  
  -- Validate target user exists
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE id = p_user) THEN
    RAISE EXCEPTION 'User % does not exist', p_user;
  END IF;
  
  -- Don't allow removing the last owner
  IF p_role != 'owner' AND EXISTS (
    SELECT 1 FROM entity_memberships em
    WHERE em.entity_id = p_entity 
    AND em.user_id = p_user 
    AND em.role = 'owner'
    AND (SELECT COUNT(*) FROM entity_memberships WHERE entity_id = p_entity AND role = 'owner') = 1
  ) THEN
    RAISE EXCEPTION 'Cannot remove the last owner of entity %', p_entity;
  END IF;
  
  -- Insert or update membership
  INSERT INTO entity_memberships (entity_id, user_id, role)
  VALUES (p_entity, p_user, p_role)
  ON CONFLICT (entity_id, user_id) 
  DO UPDATE SET 
    role = EXCLUDED.role,
    created_at = NOW();
    
  -- Log the role change
  INSERT INTO audit_events (entity_id, table_name, record_id, operation, new_values, user_id)
  VALUES (
    p_entity, 
    'entity_memberships', 
    p_entity, 
    'UPSERT',
    jsonb_build_object('user_id', p_user, 'role', p_role),
    auth.uid()
  );
END;
$$;

-- Function: Get entity summary (readonly)
CREATE OR REPLACE FUNCTION rpc_get_entity_summary(p_entity bigint)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_result jsonb;
  v_account_count integer;
  v_journal_count integer;
  v_balance_total numeric;
BEGIN
  -- Validate entity access
  IF NOT EXISTS (
    SELECT 1 FROM v_user_entities vue
    WHERE vue.entity_id = p_entity
  ) THEN
    RAISE EXCEPTION 'Access denied to entity %', p_entity;
  END IF;
  
  -- Get account count
  SELECT COUNT(*) INTO v_account_count
  FROM accounts 
  WHERE entity_id = p_entity AND is_active = true;
  
  -- Get journal count
  SELECT COUNT(*) INTO v_journal_count
  FROM journals 
  WHERE entity_id = p_entity;
  
  -- Calculate total balance (should always be 0 for balanced books)
  SELECT COALESCE(SUM(
    CASE WHEN jl.debit_amount IS NOT NULL THEN jl.debit_amount ELSE -jl.credit_amount END
  ), 0) INTO v_balance_total
  FROM journal_lines jl
  JOIN journals j ON jl.journal_id = j.id
  WHERE j.entity_id = p_entity;
  
  -- Build result
  v_result := jsonb_build_object(
    'entity_id', p_entity,
    'account_count', v_account_count,
    'journal_count', v_journal_count,
    'balance_check', v_balance_total,
    'is_balanced', ABS(v_balance_total) < 0.01,
    'generated_at', NOW()
  );
  
  RETURN v_result;
END;
$$;

-- Function: Create invoice with automatic journal entry
CREATE OR REPLACE FUNCTION rpc_create_invoice_with_journal(
  p_entity bigint,
  p_invoice_data jsonb,
  p_accounting_data jsonb
) RETURNS bigint
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_invoice_id bigint;
  v_journal_id bigint;
  v_journal_lines jsonb;
BEGIN
  -- Validate entity access
  IF NOT EXISTS (
    SELECT 1 FROM v_user_entities vue
    WHERE vue.entity_id = p_entity 
    AND vue.role IN ('owner', 'admin', 'accountant', 'bookkeeper')
  ) THEN
    RAISE EXCEPTION 'Access denied or insufficient permissions for entity %', p_entity;
  END IF;
  
  -- Create the invoice
  INSERT INTO invoices (
    entity_id,
    kind,
    number,
    counterparty_name,
    counterparty_vat,
    invoice_date,
    due_date,
    total_amount,
    vat_amount,
    status
  ) VALUES (
    p_entity,
    p_invoice_data->>'kind',
    p_invoice_data->>'number',
    p_invoice_data->>'counterparty_name',
    p_invoice_data->>'counterparty_vat',
    (p_invoice_data->>'invoice_date')::date,
    (p_invoice_data->>'due_date')::date,
    (p_invoice_data->>'total_amount')::numeric,
    (p_invoice_data->>'vat_amount')::numeric,
    COALESCE(p_invoice_data->>'status', 'draft')
  ) RETURNING id INTO v_invoice_id;
  
  -- If accounting data is provided, create journal entry
  IF p_accounting_data IS NOT NULL AND p_accounting_data->>'auto_journal' = 'true' THEN
    -- Create journal entry using the post_journal RPC
    SELECT rpc_post_journal(
      p_entity,
      CASE WHEN p_invoice_data->>'kind' = 'sale' THEN 'sales' ELSE 'purchases' END,
      p_invoice_data->>'number',
      'Invoice ' || p_invoice_data->>'number' || ' - ' || p_invoice_data->>'counterparty_name',
      (p_invoice_data->>'invoice_date')::date,
      p_accounting_data->'journal_lines'
    ) INTO v_journal_id;
    
    -- Link the journal to the invoice
    UPDATE invoices 
    SET journal_id = v_journal_id
    WHERE id = v_invoice_id;
  END IF;
  
  RETURN v_invoice_id;
END;
$$;

-- Function: Import bank transactions
CREATE OR REPLACE FUNCTION rpc_import_bank_transactions(
  p_entity bigint,
  p_bank_account_id bigint,
  p_transactions jsonb
) RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_transaction jsonb;
  v_imported integer := 0;
  v_skipped integer := 0;
  v_errors text[] := '{}';
BEGIN
  -- Validate entity access
  IF NOT EXISTS (
    SELECT 1 FROM v_user_entities vue
    WHERE vue.entity_id = p_entity 
    AND vue.role IN ('owner', 'admin', 'accountant', 'bookkeeper')
  ) THEN
    RAISE EXCEPTION 'Access denied or insufficient permissions for entity %', p_entity;
  END IF;
  
  -- Validate bank account belongs to entity
  IF NOT EXISTS (
    SELECT 1 FROM bank_accounts ba
    WHERE ba.id = p_bank_account_id AND ba.entity_id = p_entity
  ) THEN
    RAISE EXCEPTION 'Bank account % does not belong to entity %', p_bank_account_id, p_entity;
  END IF;
  
  -- Process each transaction
  FOR v_transaction IN SELECT * FROM jsonb_array_elements(p_transactions)
  LOOP
    BEGIN
      -- Try to insert transaction
      INSERT INTO bank_transactions (
        bank_account_id,
        transaction_date,
        value_date,
        amount,
        counterparty_name,
        counterparty_account,
        description,
        reference,
        transaction_id
      ) VALUES (
        p_bank_account_id,
        (v_transaction->>'transaction_date')::date,
        (v_transaction->>'value_date')::date,
        (v_transaction->>'amount')::numeric,
        v_transaction->>'counterparty_name',
        v_transaction->>'counterparty_account',
        v_transaction->>'description',
        v_transaction->>'reference',
        v_transaction->>'transaction_id'
      );
      
      v_imported := v_imported + 1;
      
    EXCEPTION 
      WHEN unique_violation THEN
        v_skipped := v_skipped + 1;
      WHEN OTHERS THEN
        v_errors := v_errors || (SQLSTATE || ': ' || SQLERRM);
    END;
  END LOOP;
  
  RETURN jsonb_build_object(
    'imported', v_imported,
    'skipped', v_skipped,
    'errors', v_errors,
    'total_processed', jsonb_array_length(p_transactions)
  );
END;
$$;

-- Function: Reconcile bank transaction
CREATE OR REPLACE FUNCTION rpc_reconcile_bank_transaction(
  p_bank_transaction_id bigint,
  p_journal_line_id bigint
) RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_entity_id bigint;
  v_bank_amount numeric;
  v_journal_amount numeric;
BEGIN
  -- Get entity and amounts for validation
  SELECT 
    ba.entity_id,
    bt.amount,
    CASE 
      WHEN jl.debit_amount IS NOT NULL THEN jl.debit_amount
      ELSE -jl.credit_amount 
    END
  INTO v_entity_id, v_bank_amount, v_journal_amount
  FROM bank_transactions bt
  JOIN bank_accounts ba ON bt.bank_account_id = ba.id
  LEFT JOIN journal_lines jl ON jl.id = p_journal_line_id
  WHERE bt.id = p_bank_transaction_id;
  
  -- Validate entity access
  IF NOT EXISTS (
    SELECT 1 FROM v_user_entities vue
    WHERE vue.entity_id = v_entity_id 
    AND vue.role IN ('owner', 'admin', 'accountant', 'bookkeeper')
  ) THEN
    RAISE EXCEPTION 'Access denied or insufficient permissions for entity %', v_entity_id;
  END IF;
  
  -- Validate amounts match (optional strict checking)
  IF ABS(v_bank_amount - v_journal_amount) > 0.01 THEN
    RAISE EXCEPTION 'Amounts do not match: bank=% journal=%', v_bank_amount, v_journal_amount;
  END IF;
  
  -- Update reconciliation
  UPDATE bank_transactions 
  SET 
    is_reconciled = true,
    journal_line_id = p_journal_line_id
  WHERE id = p_bank_transaction_id;
  
  -- Log the reconciliation
  INSERT INTO audit_events (entity_id, table_name, record_id, operation, new_values, user_id)
  VALUES (
    v_entity_id,
    'bank_transactions',
    p_bank_transaction_id,
    'RECONCILE',
    jsonb_build_object('journal_line_id', p_journal_line_id, 'reconciled_at', NOW()),
    auth.uid()
  );
END;
$$;

-- Update the all.sql file to include this new migration
\i packages/db/migrations/0002_rpcs.sql