import { JournalLine } from '@ledgerly/types'

export interface InvariantCheck {
  isValid: boolean
  violations: string[]
}

export class LedgerInvariants {
  static checkJournalBalance(lines: JournalLine[]): InvariantCheck {
    const violations: string[] = []
    
    if (lines.length < 2) {
      violations.push('Journal must have at least 2 lines')
    }
    
    const totalDebits = lines
      .filter(line => line.debit_amount !== undefined)
      .reduce((sum, line) => sum + (line.debit_amount || 0), 0)
    
    const totalCredits = lines
      .filter(line => line.credit_amount !== undefined)
      .reduce((sum, line) => sum + (line.credit_amount || 0), 0)
    
    const difference = Math.abs(totalDebits - totalCredits)
    
    if (difference > 0.01) {
      violations.push(`Journal is not balanced: debits=${totalDebits}, credits=${totalCredits}, difference=${difference}`)
    }

    // Check that each line has exactly one of debit or credit
    lines.forEach((line, index) => {
      const hasDebit = line.debit_amount !== undefined && line.debit_amount !== null
      const hasCredit = line.credit_amount !== undefined && line.credit_amount !== null
      
      if (!hasDebit && !hasCredit) {
        violations.push(`Line ${index + 1}: Must have either debit_amount or credit_amount`)
      }
      
      if (hasDebit && hasCredit) {
        violations.push(`Line ${index + 1}: Cannot have both debit_amount and credit_amount`)
      }

      if (hasDebit && (line.debit_amount! <= 0)) {
        violations.push(`Line ${index + 1}: Debit amount must be positive`)
      }

      if (hasCredit && (line.credit_amount! <= 0)) {
        violations.push(`Line ${index + 1}: Credit amount must be positive`)
      }
    })

    return {
      isValid: violations.length === 0,
      violations
    }
  }

  static checkTrialBalance(accounts: any[]): InvariantCheck {
    const violations: string[] = []
    
    let totalDebits = 0
    let totalCredits = 0
    
    accounts.forEach(account => {
      const balance = Number(account.balance || 0)
      
      // Based on normal balance, classify the balance
      if (account.normal_balance === 'debit') {
        if (balance >= 0) {
          totalDebits += balance
        } else {
          totalCredits += Math.abs(balance)
        }
      } else { // credit
        if (balance >= 0) {
          totalCredits += balance
        } else {
          totalDebits += Math.abs(balance)
        }
      }
    })
    
    const difference = Math.abs(totalDebits - totalCredits)
    
    if (difference > 0.01) {
      violations.push(`Trial balance does not balance: total debits=${totalDebits}, total credits=${totalCredits}, difference=${difference}`)
    }

    return {
      isValid: violations.length === 0,
      violations
    }
  }

  static validateAccountTypes(accounts: any[]): InvariantCheck {
    const violations: string[] = []
    const validTypes = ['asset', 'liability', 'equity', 'revenue', 'expense']
    const validBalances = ['debit', 'credit']
    
    // Expected normal balances for each account type
    const expectedNormalBalances: Record<string, string> = {
      'asset': 'debit',
      'expense': 'debit',
      'liability': 'credit',
      'equity': 'credit',
      'revenue': 'credit'
    }

    accounts.forEach(account => {
      if (!validTypes.includes(account.account_type)) {
        violations.push(`Account ${account.code} has invalid type: ${account.account_type}`)
      }
      
      if (!validBalances.includes(account.normal_balance)) {
        violations.push(`Account ${account.code} has invalid normal balance: ${account.normal_balance}`)
      }
      
      const expectedBalance = expectedNormalBalances[account.account_type]
      if (expectedBalance && account.normal_balance !== expectedBalance) {
        violations.push(`Account ${account.code} (${account.account_type}) should have normal balance '${expectedBalance}' but has '${account.normal_balance}'`)
      }
      
      // Check account code format (basic validation)
      if (!account.code || typeof account.code !== 'string' || account.code.trim() === '') {
        violations.push(`Account has invalid code: '${account.code}'`)
      }
    })

    return {
      isValid: violations.length === 0,
      violations
    }
  }

  static checkEntityDataIntegrity(entityData: {
    accounts: any[]
    journals: any[]
    trialBalance: any[]
  }): InvariantCheck {
    const allViolations: string[] = []
    
    // Check account types
    const accountCheck = this.validateAccountTypes(entityData.accounts)
    allViolations.push(...accountCheck.violations)
    
    // Check trial balance
    const trialBalanceCheck = this.checkTrialBalance(entityData.trialBalance)
    allViolations.push(...trialBalanceCheck.violations)
    
    // Check that all accounts in trial balance exist in chart of accounts
    const accountIds = new Set(entityData.accounts.map(a => a.id))
    entityData.trialBalance.forEach(tb => {
      if (!accountIds.has(tb.account_id)) {
        allViolations.push(`Trial balance references non-existent account ID: ${tb.account_id}`)
      }
    })
    
    // Check journal integrity
    entityData.journals.forEach((journal, journalIndex) => {
      if (!journal.is_balanced) {
        allViolations.push(`Journal ${journal.id} at index ${journalIndex} is marked as unbalanced`)
      }
      
      if (journal.journal_lines && journal.journal_lines.length > 0) {
        const balanceCheck = this.checkJournalBalance(journal.journal_lines)
        balanceCheck.violations.forEach(violation => {
          allViolations.push(`Journal ${journal.id}: ${violation}`)
        })
      }
    })

    return {
      isValid: allViolations.length === 0,
      violations: allViolations
    }
  }
}