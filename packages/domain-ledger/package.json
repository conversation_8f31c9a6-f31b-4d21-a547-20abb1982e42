{"name": "@ledgerly/domain-ledger", "version": "0.1.0", "private": true, "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "scripts": {"build": "tsc", "typecheck": "tsc --noEmit", "lint": "eslint src --ext .ts", "test": "jest"}, "dependencies": {"@ledgerly/types": "workspace:*"}, "devDependencies": {"@types/node": "^20.10.0", "@types/jest": "^29.5.8", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "typescript": "^5.3.3"}}