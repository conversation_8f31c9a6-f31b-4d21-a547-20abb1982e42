import { createClient, SupabaseClient } from '@supabase/supabase-js'
import type { Database } from '@ledgerly/types/src/supabase'

// Export the typed Database type for convenience
export type { Database }

// Typed Supabase client
export type SupabaseClientTyped = SupabaseClient<Database>

/**
 * Create a Supabase client with proper Database typing
 * 
 * @param url Supabase project URL
 * @param key Supabase anon or service role key
 * @returns Typed Supabase client
 */
export function createSupabaseClient(
  url: string,
  key: string
): SupabaseClientTyped {
  return createClient<Database>(url, key)
}

/**
 * Create client for browser usage (anon key)
 */
export function createBrowserClient(): SupabaseClientTyped {
  const url = process.env.NEXT_PUBLIC_SUPABASE_URL
  const key = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!url || !key) {
    throw new Error('Missing NEXT_PUBLIC_SUPABASE_URL or NEXT_PUBLIC_SUPABASE_ANON_KEY')
  }

  return createSupabaseClient(url, key)
}

/**
 * Create client for server usage (service role)
 * Only use this on the server side - NEVER in browser code
 */
export function createServerClient(): SupabaseClientTyped {
  const url = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL
  const key = process.env.SUPABASE_SERVICE_ROLE_KEY

  if (!url || !key) {
    throw new Error('Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY')
  }

  return createSupabaseClient(url, key)
}

// Default browser client for convenience (lazy initialization)
let _supabase: SupabaseClientTyped | null = null

export function getSupabase(): SupabaseClientTyped {
  if (!_supabase) {
    _supabase = createBrowserClient()
  }
  return _supabase
}

// For backward compatibility - use lazy proxy to avoid build-time initialization
export const supabase = new Proxy({} as SupabaseClientTyped, {
  get(target, prop) {
    return getSupabase()[prop as keyof SupabaseClientTyped]
  }
})
