name: CI

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

env:
  NODE_VERSION: '20'
  PYTHON_VERSION: '3.11'

jobs:
  build-test:
    name: Build and Test
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: ankane/pgvector:v0.5.1
        env:
          POSTGRES_DB: postgres
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: '**/package-lock.json'

      - name: Enable corepack
        run: corepack enable

      - name: Install pnpm
        run: corepack prepare pnpm@9 --activate

      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

      - name: Setup pnpm cache
        uses: actions/cache@v3
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.local/bin" >> $GITHUB_PATH

      - name: Verify uv installation
        run: uv --version

      - name: Setup Python environment
        working-directory: apps/workers-py
        run: |
          uv venv
          uv sync

      - name: Install system dependencies for Python workers
        run: |
          sudo apt-get update
          sudo apt-get install -y tesseract-ocr poppler-utils

      - name: Lint and typecheck JavaScript/TypeScript
        run: |
          pnpm lint
          pnpm typecheck

      - name: Build JavaScript/TypeScript projects
        run: pnpm build

      - name: Setup database environment
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/postgres
        run: |
          # Apply migrations
          psql $DATABASE_URL -f packages/db/migrations/all.sql

      - name: Run SQL invariant tests
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/postgres
        run: |
          # Run database tests
          psql $DATABASE_URL -f packages/db/tests/001_invariants.sql

      - name: Run JavaScript/TypeScript tests
        run: pnpm test

      - name: Lint Python code
        working-directory: apps/workers-py
        run: |
          source .venv/bin/activate
          uv run ruff check .

      - name: Format check Python code
        working-directory: apps/workers-py
        run: |
          source .venv/bin/activate
          uv run black --check .

      - name: Type check Python code
        working-directory: apps/workers-py
        run: |
          source .venv/bin/activate
          uv run mypy .

      - name: Test Python code
        working-directory: apps/workers-py
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/postgres
          REDIS_URL: redis://localhost:6379
        run: |
          source .venv/bin/activate
          uv run pytest

      - name: Check for security vulnerabilities
        run: |
          # Check npm audit
          pnpm audit --audit-level=moderate || true
          # Python security check would go here with safety or bandit

  sqlfluff:
    name: SQL Lint
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install sqlfluff
        run: pip install sqlfluff

      - name: Lint SQL files
        working-directory: packages/db
        run: sqlfluff lint --dialect postgres .

  build-summary:
    name: Build Summary
    runs-on: ubuntu-latest
    needs: [build-test, sqlfluff]
    if: always()
    steps:
      - name: Check build status
        run: |
          if [[ "${{ needs.build-test.result }}" == "failure" ]]; then
            echo "❌ Build and tests failed"
            exit 1
          elif [[ "${{ needs.sqlfluff.result }}" == "failure" ]]; then
            echo "❌ SQL linting failed"
            exit 1
          else
            echo "✅ All checks passed"
          fi