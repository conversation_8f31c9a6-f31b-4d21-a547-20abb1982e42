# Staging Environment Configuration
# Supabase Staging Project: ledgerly-be-staging (kqkeqzpccirdcosiqusl)

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://kqkeqzpccirdcosiqusl.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imtxa2VxenBjY2lyZGNvc2lxdXNsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYxMDc2MTEsImV4cCI6MjA3MTY4MzYxMX0.qDzK8x3SXAZNWrob7wv60ozxAD84-SziAD43KqJYRy8
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imtxa2VxenBjY2lyZGNvc2lxdXNsIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1NjEwNzYxMSwiZXhwIjoyMDcxNjgzNjExfQ.aqiCYOlaBzCV5Cib6nYAzHFpbwPNp-hUVtL6NqtMKKg

# Database Configuration
STAGING_DATABASE_URL=postgresql://postgres:[password]@db.kqkeqzpccirdcosiqusl.supabase.co:5432/postgres
DATABASE_URL=postgresql://postgres:[password]@db.kqkeqzpccirdcosiqusl.supabase.co:5432/postgres

# BFF Configuration
BFF_PORT=4000
INTERNAL_KEY=staging-secure-internal-key-change-in-production

# Workers Configuration
REDIS_URL=redis://localhost:6379

# Environment
NODE_ENV=staging
ENVIRONMENT=staging

# Security Configuration
SECURITY_MONITORING_URL=https://your-monitoring-service.com/events
SECURITY_MONITORING_TOKEN=your-monitoring-token

# Feature Flags
FEATURE_TRACK_A=true
FEATURE_ADVANCED_SECURITY=true
