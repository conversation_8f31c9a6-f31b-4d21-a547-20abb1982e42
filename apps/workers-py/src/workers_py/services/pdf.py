"""PDF processing service using pdfplumber."""

import logging
import tempfile
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple, Union
from datetime import datetime
import io

import pdfplumber
import pandas as pd
from PIL import Image

logger = logging.getLogger(__name__)


class PDFService:
    """PDF processing service for text extraction and metadata analysis."""
    
    def __init__(self):
        """Initialize PDF service."""
        self.supported_formats = ['.pdf']
        
    def extract_text_and_metadata(
        self,
        pdf_path: Path,
        extract_tables: bool = False,
        extract_images: bool = False,
        max_pages: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Extract text and metadata from PDF file.
        
        Args:
            pdf_path: Path to PDF file
            extract_tables: Whether to extract tables
            extract_images: Whether to extract images
            max_pages: Maximum number of pages to process
            
        Returns:
            Dictionary containing extracted text and metadata
        """
        try:
            logger.info(f"Processing PDF: {pdf_path}")
            
            with pdfplumber.open(pdf_path) as pdf:
                # Get basic PDF metadata
                pdf_info = pdf.metadata or {}
                
                # Initialize result structure
                result = {
                    'text': '',
                    'metadata': {
                        'extractor': 'pdfplumber',
                        'file_path': str(pdf_path),
                        'file_size': pdf_path.stat().st_size,
                        'page_count': len(pdf.pages),
                        'pages_processed': 0,
                        'extraction_method': 'native',
                        'has_text': False,
                        'pdf_info': pdf_info,
                        'creation_date': self._parse_pdf_date(pdf_info.get('CreationDate')),
                        'modification_date': self._parse_pdf_date(pdf_info.get('ModDate')),
                        'author': pdf_info.get('Author'),
                        'title': pdf_info.get('Title'),
                        'subject': pdf_info.get('Subject'),
                        'creator': pdf_info.get('Creator'),
                        'producer': pdf_info.get('Producer'),
                        'character_count': 0,
                        'word_count': 0,
                        'table_count': 0,
                        'image_count': 0
                    },
                    'pages': [],
                    'tables': [],
                    'images': []
                }
                
                # Process pages
                pages_to_process = min(len(pdf.pages), max_pages) if max_pages else len(pdf.pages)
                all_text_parts = []
                
                for page_num, page in enumerate(pdf.pages[:pages_to_process], 1):
                    try:
                        logger.debug(f"Processing page {page_num}/{pages_to_process}")
                        
                        page_result = self._process_page(
                            page,
                            page_num,
                            extract_tables=extract_tables,
                            extract_images=extract_images
                        )
                        
                        result['pages'].append(page_result)
                        
                        # Collect text
                        page_text = page_result.get('text', '')
                        if page_text.strip():
                            all_text_parts.append(f"--- Page {page_num} ---\n{page_text}")
                            result['metadata']['has_text'] = True
                        
                        # Collect tables
                        if extract_tables and page_result.get('tables'):
                            result['tables'].extend(page_result['tables'])
                        
                        # Collect images
                        if extract_images and page_result.get('images'):
                            result['images'].extend(page_result['images'])
                        
                        result['metadata']['pages_processed'] += 1
                        
                    except Exception as e:
                        logger.error(f"Failed to process page {page_num}: {e}")
                        result['pages'].append({
                            'page_number': page_num,
                            'text': '',
                            'error': str(e)
                        })
                
                # Combine all text
                combined_text = '\n\n'.join(all_text_parts)
                result['text'] = combined_text
                
                # Update metadata
                result['metadata']['character_count'] = len(combined_text)
                result['metadata']['word_count'] = len(combined_text.split())
                result['metadata']['table_count'] = len(result['tables'])
                result['metadata']['image_count'] = len(result['images'])
                
                logger.info(
                    f"PDF processing completed. Extracted {len(combined_text)} characters, "
                    f"{len(result['tables'])} tables, {len(result['images'])} images from "
                    f"{pages_to_process} pages"
                )
                
                return result
                
        except Exception as e:
            logger.error(f"PDF processing failed for {pdf_path}: {e}")
            return {
                'text': '',
                'metadata': {
                    'extractor': 'pdfplumber',
                    'error': str(e),
                    'file_path': str(pdf_path)
                },
                'pages': [],
                'tables': [],
                'images': []
            }
    
    def _process_page(
        self,
        page: pdfplumber.page.Page,
        page_num: int,
        extract_tables: bool = False,
        extract_images: bool = False
    ) -> Dict[str, Any]:
        """
        Process a single PDF page.
        
        Args:
            page: pdfplumber page object
            page_num: Page number
            extract_tables: Whether to extract tables
            extract_images: Whether to extract images
            
        Returns:
            Dictionary containing page data
        """
        result = {
            'page_number': page_num,
            'text': '',
            'metadata': {
                'width': page.width,
                'height': page.height,
                'rotation': getattr(page, 'rotation', 0),
                'character_count': 0,
                'word_count': 0
            },
            'tables': [],
            'images': []
        }
        
        try:
            # Extract text
            page_text = page.extract_text()
            if page_text:
                result['text'] = page_text.strip()
                result['metadata']['character_count'] = len(result['text'])
                result['metadata']['word_count'] = len(result['text'].split())
            
            # Extract tables
            if extract_tables:
                result['tables'] = self._extract_page_tables(page, page_num)
            
            # Extract images
            if extract_images:
                result['images'] = self._extract_page_images(page, page_num)
            
        except Exception as e:
            logger.error(f"Error processing page {page_num}: {e}")
            result['error'] = str(e)
        
        return result
    
    def _extract_page_tables(self, page: pdfplumber.page.Page, page_num: int) -> List[Dict[str, Any]]:
        """
        Extract tables from a PDF page.
        
        Args:
            page: pdfplumber page object
            page_num: Page number
            
        Returns:
            List of table dictionaries
        """
        tables = []
        
        try:
            page_tables = page.find_tables()
            
            for table_idx, table in enumerate(page_tables):
                try:
                    # Extract table data
                    table_data = table.extract()
                    
                    if not table_data:
                        continue
                    
                    # Convert to DataFrame for better handling
                    df = pd.DataFrame(table_data[1:], columns=table_data[0])
                    
                    # Clean the DataFrame
                    df = df.dropna(how='all').dropna(axis=1, how='all')
                    
                    table_info = {
                        'table_id': f"page_{page_num}_table_{table_idx + 1}",
                        'page_number': page_num,
                        'table_index': table_idx,
                        'data': df.to_dict('records'),
                        'columns': df.columns.tolist(),
                        'shape': df.shape,
                        'bbox': table.bbox,
                        'metadata': {
                            'rows': len(df),
                            'columns': len(df.columns),
                            'has_header': True,
                            'extraction_method': 'pdfplumber'
                        }
                    }
                    
                    tables.append(table_info)
                    
                except Exception as e:
                    logger.warning(f"Failed to extract table {table_idx} from page {page_num}: {e}")
        
        except Exception as e:
            logger.warning(f"Table extraction failed for page {page_num}: {e}")
        
        return tables
    
    def _extract_page_images(self, page: pdfplumber.page.Page, page_num: int) -> List[Dict[str, Any]]:
        """
        Extract images from a PDF page.
        
        Args:
            page: pdfplumber page object
            page_num: Page number
            
        Returns:
            List of image dictionaries
        """
        images = []
        
        try:
            # Get images from the page
            page_images = page.images
            
            for img_idx, img in enumerate(page_images):
                try:
                    image_info = {
                        'image_id': f"page_{page_num}_image_{img_idx + 1}",
                        'page_number': page_num,
                        'image_index': img_idx,
                        'bbox': (img['x0'], img['top'], img['x1'], img['bottom']),
                        'width': img['width'],
                        'height': img['height'],
                        'metadata': {
                            'object_id': img.get('object_id'),
                            'extraction_method': 'pdfplumber'
                        }
                    }
                    
                    images.append(image_info)
                    
                except Exception as e:
                    logger.warning(f"Failed to process image {img_idx} from page {page_num}: {e}")
        
        except Exception as e:
            logger.warning(f"Image extraction failed for page {page_num}: {e}")
        
        return images
    
    def _parse_pdf_date(self, date_str: Optional[str]) -> Optional[str]:
        """
        Parse PDF date string to ISO format.
        
        Args:
            date_str: PDF date string
            
        Returns:
            ISO formatted date string or None
        """
        if not date_str:
            return None
        
        try:
            # PDF date format: D:YYYYMMDDHHmmSSOHH'mm
            if date_str.startswith('D:'):
                date_str = date_str[2:]
            
            # Extract date parts
            if len(date_str) >= 14:
                year = int(date_str[:4])
                month = int(date_str[4:6])
                day = int(date_str[6:8])
                hour = int(date_str[8:10]) if len(date_str) >= 10 else 0
                minute = int(date_str[10:12]) if len(date_str) >= 12 else 0
                second = int(date_str[12:14]) if len(date_str) >= 14 else 0
                
                dt = datetime(year, month, day, hour, minute, second)
                return dt.isoformat()
        
        except Exception as e:
            logger.warning(f"Failed to parse PDF date '{date_str}': {e}")
        
        return None
    
    def extract_form_fields(self, pdf_path: Path) -> Dict[str, Any]:
        """
        Extract form fields from PDF if available.
        
        Args:
            pdf_path: Path to PDF file
            
        Returns:
            Dictionary containing form fields
        """
        try:
            with pdfplumber.open(pdf_path) as pdf:
                # Check if PDF has forms
                if not hasattr(pdf, 'annots') or not pdf.annots:
                    return {
                        'has_forms': False,
                        'fields': []
                    }
                
                fields = []
                for page_num, page in enumerate(pdf.pages, 1):
                    page_annots = page.annots or []
                    
                    for annot in page_annots:
                        if annot.get('Subtype') == 'Widget':
                            field_info = {
                                'page_number': page_num,
                                'field_name': annot.get('T', ''),
                                'field_type': annot.get('FT', ''),
                                'field_value': annot.get('V', ''),
                                'bbox': annot.get('Rect', [])
                            }
                            fields.append(field_info)
                
                return {
                    'has_forms': len(fields) > 0,
                    'field_count': len(fields),
                    'fields': fields
                }
        
        except Exception as e:
            logger.warning(f"Form field extraction failed: {e}")
            return {
                'has_forms': False,
                'fields': [],
                'error': str(e)
            }
    
    def get_pdf_info(self, pdf_path: Path) -> Dict[str, Any]:
        """
        Get basic PDF information without full text extraction.
        
        Args:
            pdf_path: Path to PDF file
            
        Returns:
            Dictionary containing PDF information
        """
        try:
            with pdfplumber.open(pdf_path) as pdf:
                pdf_info = pdf.metadata or {}
                
                # Get page sizes
                page_sizes = []
                for page in pdf.pages:
                    page_sizes.append({
                        'width': page.width,
                        'height': page.height,
                        'rotation': getattr(page, 'rotation', 0)
                    })
                
                return {
                    'file_path': str(pdf_path),
                    'file_size': pdf_path.stat().st_size,
                    'page_count': len(pdf.pages),
                    'metadata': pdf_info,
                    'page_sizes': page_sizes,
                    'creation_date': self._parse_pdf_date(pdf_info.get('CreationDate')),
                    'modification_date': self._parse_pdf_date(pdf_info.get('ModDate')),
                    'author': pdf_info.get('Author'),
                    'title': pdf_info.get('Title'),
                    'subject': pdf_info.get('Subject'),
                    'creator': pdf_info.get('Creator'),
                    'producer': pdf_info.get('Producer'),
                }
        
        except Exception as e:
            logger.error(f"Failed to get PDF info: {e}")
            return {
                'file_path': str(pdf_path),
                'error': str(e)
            }
    
    def is_text_based_pdf(self, pdf_path: Path, sample_pages: int = 3) -> bool:
        """
        Check if PDF contains extractable text.
        
        Args:
            pdf_path: Path to PDF file
            sample_pages: Number of pages to sample
            
        Returns:
            True if PDF has extractable text
        """
        try:
            with pdfplumber.open(pdf_path) as pdf:
                pages_to_check = min(len(pdf.pages), sample_pages)
                
                for page in pdf.pages[:pages_to_check]:
                    text = page.extract_text()
                    if text and text.strip():
                        return True
                
                return False
        
        except Exception as e:
            logger.warning(f"Text detection failed: {e}")
            return False