"""AI processing service for document analysis and categorization."""

import logging
import re
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

import numpy as np
from sentence_transformers import SentenceTransformer

from ..config import settings

logger = logging.getLogger(__name__)


class AIService:
    """AI service for document analysis, categorization, and entity extraction."""
    
    def __init__(self):
        """Initialize AI service."""
        self.model_name = settings.SENTENCE_TRANSFORMERS_MODEL
        self.confidence_threshold = 0.7
        self.model = None
        self._initialize_model()
        
        # Define document categories and their keywords
        self.document_categories = {
            'invoice': {
                'keywords': ['invoice', 'bill', 'payment due', 'amount due', 'total amount', 'subtotal'],
                'patterns': [r'invoice\s*#?\s*\d+', r'bill\s*#?\s*\d+', r'inv\s*#?\s*\d+'],
                'description': 'Financial invoice or bill'
            },
            'receipt': {
                'keywords': ['receipt', 'paid', 'transaction', 'purchase', 'payment received'],
                'patterns': [r'receipt\s*#?\s*\d+', r'transaction\s*#?\s*\d+'],
                'description': 'Payment receipt or transaction record'
            },
            'contract': {
                'keywords': ['contract', 'agreement', 'terms', 'parties', 'signature', 'execute'],
                'patterns': [r'contract\s*#?\s*\d+', r'agreement\s*#?\s*\d+'],
                'description': 'Legal contract or agreement'
            },
            'statement': {
                'keywords': ['statement', 'balance', 'account', 'period', 'summary'],
                'patterns': [r'statement\s*#?\s*\d+', r'account\s*#?\s*\d+'],
                'description': 'Financial or account statement'
            },
            'report': {
                'keywords': ['report', 'analysis', 'summary', 'findings', 'conclusion'],
                'patterns': [r'report\s*#?\s*\d+'],
                'description': 'Business or analytical report'
            },
            'correspondence': {
                'keywords': ['dear', 'sincerely', 'regards', 'letter', 'correspondence'],
                'patterns': [r'dear\s+\w+', r'sincerely,', r'best\s+regards'],
                'description': 'Business correspondence or letter'
            }
        }
        
        # Common entity patterns
        self.entity_patterns = {
            'email': r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            'phone': r'(?:\+?1[-.\s]?)?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}',
            'date': r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b|\b\d{4}[/-]\d{1,2}[/-]\d{1,2}\b',
            'amount': r'\$\s*\d{1,3}(?:,\d{3})*(?:\.\d{2})?|\b\d{1,3}(?:,\d{3})*(?:\.\d{2})?\s*(?:USD|dollars?)\b',
            'invoice_number': r'(?:invoice|inv|bill)\s*#?\s*(\d+)',
            'account_number': r'(?:account|acc|acct)\s*#?\s*(\d+)',
            'tax_id': r'(?:tax\s*id|ein|ssn)\s*:?\s*(\d{2,3}[-.]?\d{2,3}[-.]?\d{3,4})',
        }
    
    def _initialize_model(self) -> None:
        """Initialize the sentence transformer model."""
        try:
            logger.info(f"Loading sentence transformer model: {self.model_name}")
            self.model = SentenceTransformer(self.model_name)
            logger.info("AI model loaded successfully")
        except Exception as e:
            logger.error(f"Failed to load AI model: {e}")
            self.model = None
    
    def analyze_document(
        self,
        text: str,
        metadata: Optional[Dict[str, Any]] = None,
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Perform comprehensive AI analysis on document text.
        
        Args:
            text: Extracted text from document
            metadata: Document metadata
            options: Analysis options
            
        Returns:
            Dictionary containing AI analysis results
        """
        if not text or not text.strip():
            return {
                'document_type': None,
                'confidence': 0.0,
                'categories': [],
                'entities': {},
                'summary': '',
                'key_information': {},
                'language': 'unknown',
                'error': 'No text provided for analysis'
            }
        
        try:
            logger.info("Performing AI document analysis")
            
            options = options or {}
            confidence_threshold = options.get('confidence_threshold', self.confidence_threshold)
            
            # Perform different types of analysis
            document_type, type_confidence = self._classify_document_type(text)
            categories = self._categorize_document(text, confidence_threshold)
            entities = self._extract_entities(text)
            language = self._detect_language(text)
            summary = self._generate_summary(text)
            key_info = self._extract_key_information(text, document_type)
            sentiment = self._analyze_sentiment(text)
            
            # Combine results
            result = {
                'document_type': document_type if type_confidence >= confidence_threshold else None,
                'confidence': round(type_confidence, 3),
                'categories': categories,
                'entities': entities,
                'summary': summary,
                'key_information': key_info,
                'language': language,
                'sentiment': sentiment,
                'analysis_metadata': {
                    'model_name': self.model_name,
                    'confidence_threshold': confidence_threshold,
                    'text_length': len(text),
                    'word_count': len(text.split()),
                    'analysis_timestamp': datetime.utcnow().isoformat(),
                    'features_extracted': len(entities),
                }
            }
            
            logger.info(f"AI analysis completed. Document type: {document_type} ({type_confidence:.2f} confidence)")
            return result
            
        except Exception as e:
            logger.error(f"AI analysis failed: {e}")
            return {
                'document_type': None,
                'confidence': 0.0,
                'categories': [],
                'entities': {},
                'summary': '',
                'key_information': {},
                'language': 'unknown',
                'error': str(e)
            }
    
    def _classify_document_type(self, text: str) -> Tuple[str, float]:
        """
        Classify document type using keyword matching and patterns.
        
        Args:
            text: Document text
            
        Returns:
            Tuple of (document_type, confidence)
        """
        text_lower = text.lower()
        scores = {}
        
        for doc_type, config in self.document_categories.items():
            score = 0
            
            # Check keywords
            keyword_matches = sum(1 for keyword in config['keywords'] if keyword in text_lower)
            score += keyword_matches * 2
            
            # Check patterns
            pattern_matches = sum(1 for pattern in config['patterns'] if re.search(pattern, text_lower))
            score += pattern_matches * 3
            
            # Normalize score
            max_possible = len(config['keywords']) * 2 + len(config['patterns']) * 3
            scores[doc_type] = score / max_possible if max_possible > 0 else 0
        
        if not scores:
            return 'unknown', 0.0
        
        best_type = max(scores, key=scores.get)
        confidence = scores[best_type]
        
        return best_type, confidence
    
    def _categorize_document(self, text: str, threshold: float) -> List[str]:
        """
        Categorize document into multiple categories.
        
        Args:
            text: Document text
            threshold: Confidence threshold
            
        Returns:
            List of categories
        """
        categories = []
        text_lower = text.lower()
        
        # Simple keyword-based categorization
        category_keywords = {
            'financial': ['invoice', 'payment', 'amount', 'cost', 'price', 'bill', 'receipt'],
            'legal': ['contract', 'agreement', 'terms', 'legal', 'signature', 'party'],
            'business': ['company', 'business', 'corporate', 'organization', 'firm'],
            'personal': ['personal', 'individual', 'private', 'confidential'],
            'technical': ['technical', 'specification', 'manual', 'guide', 'documentation'],
            'correspondence': ['letter', 'email', 'message', 'communication', 'dear'],
        }
        
        for category, keywords in category_keywords.items():
            keyword_count = sum(1 for keyword in keywords if keyword in text_lower)
            confidence = keyword_count / len(keywords)
            
            if confidence >= threshold:
                categories.append(category)
        
        return categories
    
    def _extract_entities(self, text: str) -> Dict[str, Any]:
        """
        Extract entities from text using regex patterns.
        
        Args:
            text: Document text
            
        Returns:
            Dictionary of extracted entities
        """
        entities = {}
        
        for entity_type, pattern in self.entity_patterns.items():
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                # Remove duplicates and empty matches
                unique_matches = list(set(match.strip() for match in matches if match.strip()))
                if unique_matches:
                    entities[entity_type] = unique_matches
        
        # Extract additional business entities
        entities.update(self._extract_business_entities(text))
        
        return entities
    
    def _extract_business_entities(self, text: str) -> Dict[str, Any]:
        """
        Extract business-specific entities.
        
        Args:
            text: Document text
            
        Returns:
            Dictionary of business entities
        """
        entities = {}
        
        # Company names (simple heuristic)
        company_patterns = [
            r'\b([A-Z][a-z]+ (?:Inc|LLC|Corp|Corporation|Ltd|Limited|Company|Co)\.?)\b',
            r'\b([A-Z][a-z]+ [A-Z][a-z]+ (?:Inc|LLC|Corp|Corporation|Ltd|Limited|Company|Co)\.?)\b'
        ]
        
        companies = []
        for pattern in company_patterns:
            matches = re.findall(pattern, text)
            companies.extend(matches)
        
        if companies:
            entities['companies'] = list(set(companies))
        
        # Addresses (simple pattern)
        address_pattern = r'\d+\s+[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Drive|Dr|Lane|Ln|Boulevard|Blvd)\.?'
        addresses = re.findall(address_pattern, text)
        if addresses:
            entities['addresses'] = list(set(addresses))
        
        # Percentages
        percentage_pattern = r'\b\d{1,3}(?:\.\d{1,2})?%'
        percentages = re.findall(percentage_pattern, text)
        if percentages:
            entities['percentages'] = list(set(percentages))
        
        return entities
    
    def _detect_language(self, text: str) -> str:
        """
        Detect document language (simple heuristic).
        
        Args:
            text: Document text
            
        Returns:
            Language code
        """
        # Simple language detection based on common words
        english_words = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']
        spanish_words = ['el', 'la', 'y', 'o', 'pero', 'en', 'de', 'con', 'por', 'para', 'que', 'es']
        french_words = ['le', 'la', 'et', 'ou', 'mais', 'dans', 'de', 'avec', 'par', 'pour', 'que', 'est']
        
        text_lower = text.lower()
        words = text_lower.split()
        
        if len(words) < 10:
            return 'unknown'
        
        # Count language-specific words
        english_count = sum(1 for word in english_words if word in words)
        spanish_count = sum(1 for word in spanish_words if word in words)
        french_count = sum(1 for word in french_words if word in words)
        
        max_count = max(english_count, spanish_count, french_count)
        
        if max_count == 0:
            return 'unknown'
        elif max_count == english_count:
            return 'en'
        elif max_count == spanish_count:
            return 'es'
        elif max_count == french_count:
            return 'fr'
        else:
            return 'unknown'
    
    def _generate_summary(self, text: str, max_sentences: int = 3) -> str:
        """
        Generate a simple summary of the document.
        
        Args:
            text: Document text
            max_sentences: Maximum number of sentences in summary
            
        Returns:
            Document summary
        """
        if not text.strip():
            return ''
        
        # Simple extractive summarization
        sentences = re.split(r'[.!?]+', text)
        sentences = [s.strip() for s in sentences if s.strip() and len(s.strip()) > 20]
        
        if not sentences:
            return text[:200] + '...' if len(text) > 200 else text
        
        # Take first few sentences as summary
        summary_sentences = sentences[:max_sentences]
        summary = '. '.join(summary_sentences)
        
        # Ensure it doesn't end abruptly
        if not summary.endswith('.'):
            summary += '.'
        
        return summary
    
    def _extract_key_information(self, text: str, document_type: str) -> Dict[str, Any]:
        """
        Extract key information based on document type.
        
        Args:
            text: Document text
            document_type: Detected document type
            
        Returns:
            Dictionary of key information
        """
        key_info = {}
        
        if document_type == 'invoice':
            # Extract invoice-specific information
            key_info.update(self._extract_invoice_info(text))
        elif document_type == 'receipt':
            # Extract receipt-specific information
            key_info.update(self._extract_receipt_info(text))
        elif document_type == 'contract':
            # Extract contract-specific information
            key_info.update(self._extract_contract_info(text))
        
        return key_info
    
    def _extract_invoice_info(self, text: str) -> Dict[str, Any]:
        """Extract invoice-specific information."""
        info = {}
        
        # Extract amounts
        amount_pattern = r'\$\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)'
        amounts = re.findall(amount_pattern, text)
        if amounts:
            info['amounts'] = amounts
            # Try to identify the total
            total_pattern = r'total[:\s]*\$\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)'
            total_match = re.search(total_pattern, text, re.IGNORECASE)
            if total_match:
                info['total_amount'] = total_match.group(1)
        
        return info
    
    def _extract_receipt_info(self, text: str) -> Dict[str, Any]:
        """Extract receipt-specific information."""
        info = {}
        
        # Similar to invoice but focus on payment confirmation
        payment_pattern = r'paid[:\s]*\$\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)'
        payment_match = re.search(payment_pattern, text, re.IGNORECASE)
        if payment_match:
            info['payment_amount'] = payment_match.group(1)
        
        return info
    
    def _extract_contract_info(self, text: str) -> Dict[str, Any]:
        """Extract contract-specific information."""
        info = {}
        
        # Extract parties
        party_pattern = r'party[:\s]+([A-Za-z\s]+(?:Inc|LLC|Corp|Corporation|Ltd|Limited|Company|Co)\.?)'
        parties = re.findall(party_pattern, text, re.IGNORECASE)
        if parties:
            info['parties'] = list(set(parties))
        
        return info
    
    def _analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """
        Perform basic sentiment analysis.
        
        Args:
            text: Document text
            
        Returns:
            Sentiment analysis results
        """
        # Simple rule-based sentiment analysis
        positive_words = ['good', 'excellent', 'positive', 'satisfied', 'happy', 'success', 'approved']
        negative_words = ['bad', 'poor', 'negative', 'unsatisfied', 'unhappy', 'failed', 'rejected']
        
        text_lower = text.lower()
        words = text_lower.split()
        
        positive_count = sum(1 for word in positive_words if word in words)
        negative_count = sum(1 for word in negative_words if word in words)
        
        if positive_count > negative_count:
            sentiment = 'positive'
            confidence = positive_count / (positive_count + negative_count)
        elif negative_count > positive_count:
            sentiment = 'negative'
            confidence = negative_count / (positive_count + negative_count)
        else:
            sentiment = 'neutral'
            confidence = 0.5
        
        return {
            'sentiment': sentiment,
            'confidence': round(confidence, 3),
            'positive_indicators': positive_count,
            'negative_indicators': negative_count
        }
    
    def compute_embeddings(self, texts: List[str]) -> Optional[np.ndarray]:
        """
        Compute embeddings for texts using sentence transformer.
        
        Args:
            texts: List of texts to embed
            
        Returns:
            Numpy array of embeddings or None if model not available
        """
        if not self.model:
            logger.warning("Sentence transformer model not available")
            return None
        
        try:
            embeddings = self.model.encode(texts)
            return embeddings
        except Exception as e:
            logger.error(f"Failed to compute embeddings: {e}")
            return None