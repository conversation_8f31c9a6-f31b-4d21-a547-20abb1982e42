"""Pytest configuration and fixtures."""

import asyncio
import tempfile
from pathlib import Path
from typing import As<PERSON><PERSON>enerator, Generator
from unittest.mock import AsyncMock, MagicMock

import pytest
import pytest_asyncio
from httpx import AsyncClient
from fastapi.testclient import TestClient

from workers_py.main import app
from workers_py.config import Settings


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def test_settings():
    """Test settings fixture."""
    return Settings(
        DEBUG=True,
        LOG_LEVEL="debug",
        DATABASE_URL="sqlite:///test.db",
        REDIS_URL="redis://localhost:6379/15",  # Use test database
        CELERY_BROKER_URL="redis://localhost:6379/15",
        CELERY_RESULT_BACKEND="redis://localhost:6379/15",
        ENVIRONMENT="testing"
    )


@pytest.fixture
def client() -> Generator[TestClient, None, None]:
    """Test client fixture."""
    with TestClient(app) as test_client:
        yield test_client


@pytest_asyncio.fixture
async def async_client() -> AsyncGenerator[AsyncClient, None]:
    """Async test client fixture."""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac


@pytest.fixture
def mock_database():
    """Mock database fixture."""
    mock_db = AsyncMock()
    mock_db.execute = AsyncMock()
    mock_db.fetch = AsyncMock()
    mock_db.fetchrow = AsyncMock()
    return mock_db


@pytest.fixture
def temp_pdf_file():
    """Create a temporary PDF file for testing."""
    # This would create a simple test PDF
    # For now, we'll create a text file with PDF extension
    with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp:
        tmp.write(b'%PDF-1.4\n%Fake PDF for testing\nSample text content')
        tmp_path = Path(tmp.name)
    
    yield tmp_path
    
    # Cleanup
    tmp_path.unlink(missing_ok=True)


@pytest.fixture
def temp_image_file():
    """Create a temporary image file for testing."""
    from PIL import Image
    
    # Create a simple test image
    image = Image.new('RGB', (100, 100), color='white')
    
    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp:
        image.save(tmp.name)
        tmp_path = Path(tmp.name)
    
    yield tmp_path
    
    # Cleanup
    tmp_path.unlink(missing_ok=True)


@pytest.fixture
def sample_document_text():
    """Sample document text for testing."""
    return """
    INVOICE #12345
    
    Date: 2023-12-01
    Due Date: 2023-12-31
    
    Bill To:
    Acme Corporation
    123 Main Street
    Anytown, ST 12345
    
    Description: Professional Services
    Amount: $1,234.56
    Tax: $123.46
    Total: $1,358.02
    
    Please remit payment by the due date.
    
    Thank you for your business!
    """


@pytest.fixture
def mock_celery_task():
    """Mock Celery task fixture."""
    mock_task = MagicMock()
    mock_task.id = "test-task-123"
    mock_task.state = "SUCCESS"
    mock_task.result = {"status": "completed", "message": "Task completed successfully"}
    mock_task.delay = MagicMock(return_value=mock_task)
    return mock_task


@pytest.fixture
def mock_ocr_service():
    """Mock OCR service fixture."""
    mock_service = MagicMock()
    mock_service.extract_text_from_image.return_value = {
        'text': 'Sample OCR text',
        'metadata': {
            'ocr_engine': 'tesseract',
            'average_confidence': 95.5,
            'character_count': 15,
            'word_count': 3
        },
        'words': []
    }
    mock_service.extract_text_from_pdf.return_value = {
        'text': 'Sample PDF OCR text',
        'metadata': {
            'ocr_engine': 'tesseract',
            'pages_processed': 1,
            'average_confidence': 90.0,
            'character_count': 19,
            'word_count': 4
        },
        'pages': [],
        'words': []
    }
    return mock_service


@pytest.fixture
def mock_pdf_service():
    """Mock PDF service fixture."""
    mock_service = MagicMock()
    mock_service.extract_text_and_metadata.return_value = {
        'text': 'Sample PDF text content',
        'metadata': {
            'extractor': 'pdfplumber',
            'page_count': 1,
            'pages_processed': 1,
            'has_text': True,
            'character_count': 23,
            'word_count': 4,
            'table_count': 0,
            'image_count': 0
        },
        'pages': [],
        'tables': [],
        'images': []
    }
    mock_service.is_text_based_pdf.return_value = True
    return mock_service


@pytest.fixture
def mock_ai_service():
    """Mock AI service fixture."""
    mock_service = MagicMock()
    mock_service.analyze_document.return_value = {
        'document_type': 'invoice',
        'confidence': 0.95,
        'categories': ['financial', 'business'],
        'entities': {
            'invoice_number': ['12345'],
            'amount': ['$1,234.56'],
            'date': ['2023-12-01']
        },
        'summary': 'Invoice #12345 for professional services totaling $1,234.56',
        'key_information': {
            'total_amount': '1234.56',
            'invoice_number': '12345'
        },
        'language': 'en',
        'sentiment': {
            'sentiment': 'neutral',
            'confidence': 0.5
        }
    }
    return mock_service


@pytest.fixture
def sample_request_data():
    """Sample request data for testing."""
    return {
        'entity_id': 'test-entity-123',
        'file_url': 'https://example.com/test-document.pdf',
        'options': {
            'ocr_language': 'eng',
            'extract_tables': True,
            'ai_categorize': True,
            'confidence_threshold': 0.8
        }
    }


# Async fixtures
@pytest_asyncio.fixture
async def mock_async_database():
    """Mock async database fixture."""
    mock_db = AsyncMock()
    
    # Mock connection context manager
    async def mock_get_database():
        return mock_db
    
    return mock_get_database


# Integration test fixtures
@pytest.fixture(scope="session")
def docker_compose_file():
    """Docker compose file for integration tests."""
    return Path(__file__).parent.parent / "docker-compose.yml"


@pytest.fixture(scope="session")
def docker_services():
    """Start docker services for integration tests."""
    # This would be used with pytest-docker for full integration tests
    # For now, we'll skip this and use mocks
    pass