"""Tests for Celery tasks."""

import tempfile
from pathlib import Path
from unittest.mock import patch, MagicMock, AsyncMock

import pytest

from workers_py.tasks import process_document_task, download_file, cleanup_temp_files


class TestDownloadFile:
    """Test file download functionality."""
    
    def test_download_file_success(self):
        """Test successful file download."""
        with patch('workers_py.tasks.httpx') as mock_httpx:
            # Mock successful HTTP response
            mock_response = MagicMock()
            mock_response.headers = {
                'content-length': '1024',
                'content-type': 'application/pdf'
            }
            mock_response.iter_bytes.return_value = [b'fake pdf content']
            mock_response.raise_for_status.return_value = None
            
            mock_httpx.stream.return_value.__enter__.return_value = mock_response
            
            # Mock tempfile
            with patch('workers_py.tasks.tempfile.NamedTemporaryFile') as mock_temp:
                mock_file = MagicMock()
                mock_file.name = '/tmp/test_file.pdf'
                mock_temp.return_value = mock_file
                
                result = download_file('https://example.com/test.pdf')
                
                assert isinstance(result, Path)
                assert str(result) == '/tmp/test_file.pdf'
                mock_file.write.assert_called_once_with(b'fake pdf content')
                mock_file.close.assert_called_once()
    
    def test_download_file_too_large(self):
        """Test download failure when file is too large."""
        with patch('workers_py.tasks.httpx') as mock_httpx:
            # Mock response with large content-length
            mock_response = MagicMock()
            mock_response.headers = {'content-length': '100000000'}  # 100MB
            
            mock_httpx.stream.return_value.__enter__.return_value = mock_response
            
            with pytest.raises(ValueError, match="File too large"):
                download_file('https://example.com/large_file.pdf', max_size_mb=50)
    
    def test_download_file_http_error(self):
        """Test download failure with HTTP error."""
        with patch('workers_py.tasks.httpx') as mock_httpx:
            # Mock HTTP error
            mock_response = MagicMock()
            mock_response.raise_for_status.side_effect = Exception("HTTP 404 Not Found")
            
            mock_httpx.stream.return_value.__enter__.return_value = mock_response
            
            with pytest.raises(Exception):
                download_file('https://example.com/nonexistent.pdf')
    
    def test_download_file_streaming_too_large(self):
        """Test download failure when streaming content exceeds size limit."""
        with patch('workers_py.tasks.httpx') as mock_httpx:
            # Mock response without content-length but with large streaming content
            mock_response = MagicMock()
            mock_response.headers = {}
            mock_response.iter_bytes.return_value = [b'x' * 10000000]  # 10MB chunk
            mock_response.raise_for_status.return_value = None
            
            mock_httpx.stream.return_value.__enter__.return_value = mock_response
            
            with patch('workers_py.tasks.tempfile.NamedTemporaryFile') as mock_temp:
                mock_file = MagicMock()
                mock_file.name = '/tmp/test_file.pdf'
                mock_temp.return_value = mock_file
                
                with pytest.raises(ValueError, match="File too large"):
                    download_file('https://example.com/large_streaming_file.pdf', max_size_mb=5)


class TestProcessDocumentTask:
    """Test document processing task."""
    
    @patch('workers_py.tasks.download_file')
    @patch('workers_py.tasks.OCRService')
    @patch('workers_py.tasks.PDFService')  
    @patch('workers_py.tasks.AIService')
    def test_process_document_task_pdf_success(
        self, 
        mock_ai_service, 
        mock_pdf_service, 
        mock_ocr_service, 
        mock_download
    ):
        """Test successful PDF document processing."""
        # Mock download
        temp_pdf = Path('/tmp/test.pdf')
        mock_download.return_value = temp_pdf
        
        # Mock PDF service
        mock_pdf_instance = mock_pdf_service.return_value
        mock_pdf_instance.extract_text_and_metadata.return_value = {
            'text': 'Sample PDF text content',
            'metadata': {'page_count': 1, 'extractor': 'pdfplumber'}
        }
        
        # Mock AI service
        mock_ai_instance = mock_ai_service.return_value
        mock_ai_instance.analyze_document.return_value = {
            'document_type': 'invoice',
            'confidence': 0.95,
            'categories': ['financial'],
            'entities': {'invoice_number': ['12345']},
            'summary': 'Invoice document',
            'key_information': {},
            'language': 'en'
        }
        
        # Create mock task instance
        mock_task = MagicMock()
        mock_task.update_state = MagicMock()
        
        # Execute task
        result = process_document_task(
            mock_task,
            entity_id='test-entity',
            file_url='https://example.com/test.pdf',
            options={}
        )
        
        # Verify result
        assert result['status'] == 'completed'
        assert result['entity_id'] == 'test-entity'
        assert result['extracted_text'] == 'Sample PDF text content'
        assert result['ai_analysis']['document_type'] == 'invoice'
        assert result['error'] is None
        
        # Verify services were called
        mock_download.assert_called_once()
        mock_pdf_instance.extract_text_and_metadata.assert_called_once()
        mock_ai_instance.analyze_document.assert_called_once()
    
    @patch('workers_py.tasks.download_file')
    @patch('workers_py.tasks.OCRService')
    @patch('workers_py.tasks.PDFService')
    @patch('workers_py.tasks.AIService')
    def test_process_document_task_image_success(
        self, 
        mock_ai_service, 
        mock_pdf_service, 
        mock_ocr_service, 
        mock_download
    ):
        """Test successful image document processing."""
        # Mock download
        temp_image = Path('/tmp/test.png')
        mock_download.return_value = temp_image
        
        # Mock OCR service
        mock_ocr_instance = mock_ocr_service.return_value
        mock_ocr_instance.extract_text_from_image.return_value = {
            'text': 'OCR extracted text',
            'metadata': {'ocr_engine': 'tesseract', 'confidence': 95.0}
        }
        
        # Mock AI service
        mock_ai_instance = mock_ai_service.return_value
        mock_ai_instance.analyze_document.return_value = {
            'document_type': 'receipt',
            'confidence': 0.85,
            'categories': ['financial'],
            'entities': {},
            'summary': 'Receipt document',
            'key_information': {},
            'language': 'en'
        }
        
        # Create mock task instance
        mock_task = MagicMock()
        mock_task.update_state = MagicMock()
        
        # Execute task
        result = process_document_task(
            mock_task,
            entity_id='test-entity',
            file_url='https://example.com/test.png',
            options={}
        )
        
        # Verify result
        assert result['status'] == 'completed'
        assert result['extracted_text'] == 'OCR extracted text'
        assert result['ai_analysis']['document_type'] == 'receipt'
        
        # Verify OCR was used, not PDF service
        mock_ocr_instance.extract_text_from_image.assert_called_once()
        mock_pdf_instance = mock_pdf_service.return_value
        mock_pdf_instance.extract_text_and_metadata.assert_not_called()
    
    @patch('workers_py.tasks.download_file')
    @patch('workers_py.tasks.OCRService')
    @patch('workers_py.tasks.PDFService')
    @patch('workers_py.tasks.AIService')
    def test_process_document_task_pdf_fallback_to_ocr(
        self, 
        mock_ai_service, 
        mock_pdf_service, 
        mock_ocr_service, 
        mock_download
    ):
        """Test PDF processing falling back to OCR when no text is extracted."""
        # Mock download
        temp_pdf = Path('/tmp/test.pdf')
        mock_download.return_value = temp_pdf
        
        # Mock PDF service returning no text
        mock_pdf_instance = mock_pdf_service.return_value
        mock_pdf_instance.extract_text_and_metadata.return_value = {
            'text': '',  # No text extracted
            'metadata': {'page_count': 1, 'extractor': 'pdfplumber'}
        }
        
        # Mock OCR service as fallback
        mock_ocr_instance = mock_ocr_service.return_value
        mock_ocr_instance.extract_text_from_pdf.return_value = {
            'text': 'OCR fallback text',
            'metadata': {'ocr_engine': 'tesseract', 'pages_processed': 1}
        }
        
        # Mock AI service
        mock_ai_instance = mock_ai_service.return_value
        mock_ai_instance.analyze_document.return_value = {
            'document_type': 'unknown',
            'confidence': 0.5,
            'categories': [],
            'entities': {},
            'summary': '',
            'key_information': {},
            'language': 'en'
        }
        
        # Create mock task instance
        mock_task = MagicMock()
        mock_task.update_state = MagicMock()
        
        # Execute task
        result = process_document_task(
            mock_task,
            entity_id='test-entity',
            file_url='https://example.com/scanned.pdf',
            options={}
        )
        
        # Verify OCR fallback was used
        assert result['extracted_text'] == 'OCR fallback text'
        mock_pdf_instance.extract_text_and_metadata.assert_called_once()
        mock_ocr_instance.extract_text_from_pdf.assert_called_once()
    
    @patch('workers_py.tasks.download_file')
    def test_process_document_task_download_failure(self, mock_download):
        """Test task failure when download fails."""
        # Mock download failure
        mock_download.side_effect = Exception("Download failed")
        
        # Create mock task instance
        mock_task = MagicMock()
        mock_task.update_state = MagicMock()
        
        # Execute task
        result = process_document_task(
            mock_task,
            entity_id='test-entity',
            file_url='https://example.com/nonexistent.pdf',
            options={}
        )
        
        # Verify failure
        assert result['status'] == 'failed'
        assert result['error'] is not None
        assert 'Download failed' in result['error']
        
        # Verify task state was updated
        mock_task.update_state.assert_called_with(
            state='FAILURE',
            meta={'error': 'Download failed', 'entity_id': 'test-entity'}
        )
    
    @patch('workers_py.tasks.download_file')
    def test_process_document_task_unsupported_file_type(self, mock_download):
        """Test task failure with unsupported file type."""
        # Mock download returning unsupported file
        temp_file = Path('/tmp/test.txt')
        mock_download.return_value = temp_file
        
        # Create mock task instance
        mock_task = MagicMock()
        mock_task.update_state = MagicMock()
        
        # Execute task
        result = process_document_task(
            mock_task,
            entity_id='test-entity',
            file_url='https://example.com/document.txt',
            options={}
        )
        
        # Verify failure
        assert result['status'] == 'failed'
        assert result['error'] is not None
        assert 'Unsupported file type' in result['error']
    
    @patch('workers_py.tasks.download_file')
    @patch('workers_py.tasks.OCRService')
    @patch('workers_py.tasks.PDFService')
    @patch('workers_py.tasks.AIService')
    def test_process_document_task_progress_updates(
        self, 
        mock_ai_service, 
        mock_pdf_service, 
        mock_ocr_service, 
        mock_download
    ):
        """Test that task progress is updated correctly."""
        # Mock services
        temp_pdf = Path('/tmp/test.pdf')
        mock_download.return_value = temp_pdf
        
        mock_pdf_instance = mock_pdf_service.return_value
        mock_pdf_instance.extract_text_and_metadata.return_value = {
            'text': 'Sample text',
            'metadata': {}
        }
        
        mock_ai_instance = mock_ai_service.return_value
        mock_ai_instance.analyze_document.return_value = {
            'document_type': 'document',
            'confidence': 0.8,
            'categories': [],
            'entities': {},
            'summary': '',
            'key_information': {},
            'language': 'en'
        }
        
        # Create mock task instance
        mock_task = MagicMock()
        mock_task.update_state = MagicMock()
        
        # Execute task
        process_document_task(
            mock_task,
            entity_id='test-entity',
            file_url='https://example.com/test.pdf',
            options={}
        )
        
        # Verify progress updates were made
        progress_calls = mock_task.update_state.call_args_list
        assert len(progress_calls) >= 3  # At least downloading, processing, ai_analysis stages
        
        # Check that progress values increase
        stages = [call[1]['meta']['stage'] for call in progress_calls if call[0] == ('PROGRESS',)]
        assert 'downloading' in stages
        assert 'processing' in stages
        assert 'ai_analysis' in stages


class TestCleanupTempFiles:
    """Test temporary file cleanup task."""
    
    @patch('workers_py.tasks.Path')
    @patch('workers_py.tasks.tempfile')
    def test_cleanup_temp_files_success(self, mock_tempfile, mock_path):
        """Test successful cleanup of old temporary files."""
        # Mock tempfile directory
        mock_temp_dir = MagicMock()
        mock_tempfile.gettempdir.return_value = '/tmp'
        mock_path.return_value = mock_temp_dir
        
        # Mock old temp files
        old_file1 = MagicMock()
        old_file1.is_file.return_value = True
        old_file1.stat.return_value.st_mtime = 1000000  # Old timestamp
        old_file1.unlink = MagicMock()
        
        old_file2 = MagicMock()
        old_file2.is_file.return_value = True
        old_file2.stat.return_value.st_mtime = 1000000  # Old timestamp
        old_file2.unlink = MagicMock()
        
        mock_temp_dir.glob.return_value = [old_file1, old_file2]
        
        # Mock current time
        with patch('workers_py.tasks.logger') as mock_logger:
            mock_logger.info.return_value.timestamp.return_value = 2000000  # Current timestamp
            
            result = cleanup_temp_files()
            
            # Verify cleanup
            assert result['cleaned_files'] == 2
            old_file1.unlink.assert_called_once()
            old_file2.unlink.assert_called_once()
    
    @patch('workers_py.tasks.Path')
    @patch('workers_py.tasks.tempfile')
    def test_cleanup_temp_files_error(self, mock_tempfile, mock_path):
        """Test cleanup task error handling."""
        # Mock tempfile directory
        mock_tempfile.gettempdir.return_value = '/tmp'
        mock_path.return_value = None
        mock_path.side_effect = Exception("Directory access failed")
        
        result = cleanup_temp_files()
        
        # Verify error handling
        assert 'error' in result
        assert result['cleaned_files'] == 0