[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "workers-py"
version = "0.1.0"
description = "Python AI workers service for document processing"
readme = "README.md"
requires-python = ">=3.11"
authors = [
    { name = "Ledgerly Team", email = "<EMAIL>" },
]
license = { text = "MIT" }
keywords = ["ai", "document-processing", "ocr", "pdf", "workers"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "celery>=5.3.0",
    "redis>=5.0.0",
    "httpx>=0.25.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "pytesseract>=0.3.10",
    "pdfplumber>=0.10.0",
    "numpy>=1.24.0",
    "pandas>=2.0.0",
    "pgvector>=0.2.0",
    "sentence-transformers>=2.2.0",
    "psycopg2-binary>=2.9.0",
    "asyncpg>=0.29.0",
    "pillow>=10.0.0",
    "python-multipart>=0.0.6",
    "pdf2image>=1.17.0",
]

[project.optional-dependencies]
dev = [
    "ruff>=0.1.0",
    "black>=23.0.0",
    "mypy>=1.7.0",
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "httpx>=0.25.0",
]

[project.urls]
Homepage = "https://github.com/ledgerly/ledgerly"
Repository = "https://github.com/ledgerly/ledgerly"
Documentation = "https://docs.ledgerly.com"

[project.scripts]
workers-py = "workers_py.main:app"

[tool.hatch.build.targets.wheel]
packages = ["src/workers_py"]

[tool.hatch.build.targets.sdist]
include = [
    "/src",
    "/tests",
    "/README.md",
]

[tool.ruff]
target-version = "py311"
line-length = 88
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]

[tool.black]
target-version = ["py311"]
line-length = 88

[tool.mypy]
python_version = "3.11"
check_untyped_defs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_return_any = true
strict_optional = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --cov=src"
testpaths = ["tests"]
asyncio_mode = "auto"

[tool.coverage.run]
source = ["src"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
]
