import Fastify from 'fastify'
import cors from '@fastify/cors'
import env from '@fastify/env'

declare module 'fastify' {
  interface FastifyInstance {
    config: {
      BFF_PORT: string
      SUPABASE_URL: string
      SUPABASE_SERVICE_ROLE_KEY: string
      INTERNAL_KEY: string
    }
  }
}

// Import plugins
import supabasePlugin from './plugins/supabase.js'
import authPlugin from './plugins/auth.js'

// Import routes
import healthRoutes from './routes/health.js'
import rpcRoutes from './routes/rpc.js'
import inboxRoutes from './routes/inbox.js'

const envSchema = {
  type: 'object',
  required: ['BFF_PORT', 'SUPABASE_URL', 'SUPABASE_SERVICE_ROLE_KEY', 'INTERNAL_KEY'],
  properties: {
    BFF_PORT: {
      type: 'string',
      default: '3001'
    },
    SUPABASE_URL: {
      type: 'string'
    },
    SUPABASE_SERVICE_ROLE_KEY: {
      type: 'string'
    },
    INTERNAL_KEY: {
      type: 'string'
    }
  }
}

async function buildApp() {
  const loggerOptions = process.env.NODE_ENV === 'development'
    ? {
        level: 'debug' as const,
        transport: {
          target: 'pino-pretty',
          options: {
            translateTime: 'HH:MM:ss Z',
            ignore: 'pid,hostname'
          }
        }
      }
    : {
        level: 'info' as const
      }

  const fastify = Fastify({
    logger: loggerOptions
  })

  // Register environment variables
  await fastify.register(env, {
    schema: envSchema,
    dotenv: true
  })

  // Register CORS
  await fastify.register(cors, {
    origin: process.env.NODE_ENV === 'development'
      ? ['http://localhost:3000', 'http://127.0.0.1:3000']
      : false,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Internal-Key']
  })

  // Register plugins
  await fastify.register(supabasePlugin, {
    url: fastify.config.SUPABASE_URL,
    serviceRoleKey: fastify.config.SUPABASE_SERVICE_ROLE_KEY
  })

  await fastify.register(authPlugin, {
    internalKey: fastify.config.INTERNAL_KEY
  })

  // Register routes
  await fastify.register(healthRoutes)
  await fastify.register(rpcRoutes)
  await fastify.register(inboxRoutes)

  // Global error handler
  fastify.setErrorHandler(async (error, _request, reply) => {
    fastify.log.error(error)

    // Handle validation errors
    if (error.validation) {
      void reply.status(400).send({
        success: false,
        error: 'Validation failed',
        details: error.validation
      })
      return
    }

    // Handle other errors
    const statusCode = error.statusCode || 500
    void reply.status(statusCode).send({
      success: false,
      error: error.message || 'Internal Server Error'
    })
  })

  // Not found handler
  fastify.setNotFoundHandler(async (_request, reply) => {
    void reply.status(404).send({
      success: false,
      error: 'Route not found'
    })
  })

  return fastify
}

async function start() {
  try {
    const fastify = await buildApp()

    const port = parseInt(fastify.config.BFF_PORT, 10)
    const host = process.env.NODE_ENV === 'production' ? '0.0.0.0' : '127.0.0.1'

    await fastify.listen({ port, host })

    fastify.log.info(`🚀 BFF server listening on ${host}:${port}`)

    // Graceful shutdown
    const signals: NodeJS.Signals[] = ['SIGINT', 'SIGTERM']
    signals.forEach((signal) => {
      process.on(signal, async () => {
        fastify.log.info(`Received ${signal}, shutting down gracefully`)
        await fastify.close()
        process.exit(0)
      })
    })

  } catch (error) {
    console.error('Error starting server:', error)
    process.exit(1)
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  void start()
}

export { buildApp }
