import { FastifyInstance } from 'fastify'
import {
  DocumentUploadRequest,
  ConfirmRequest,
  FeatureFlagRequest,
  ApiResponse
} from '@ledgerly/types'
import { z } from 'zod'

// Query parameter schemas
const EntityParamsSchema = z.object({
  entityId: z.string().transform(val => parseInt(val, 10))
})

const DocumentParamsSchema = z.object({
  documentId: z.string().transform(val => parseInt(val, 10))
})

const DocumentListQuerySchema = z.object({
  status: z.string().optional(),
  page: z.string().transform(val => parseInt(val, 10)).optional().default('1'),
  limit: z.string().transform(val => parseInt(val, 10)).optional().default('20')
})

export default async function inboxRoutes(fastify: FastifyInstance) {
  // Helper function to verify feature flag
  const checkFeatureFlag = async (supabaseClient: any, entityId: number, flag: string) => {
    const { data } = await supabaseClient
      .from('feature_flags')
      .select('enabled, config')
      .eq('entity_id', entityId)
      .eq('flag', flag)
      .single()

    return { enabled: data?.enabled || false, config: data?.config || {} }
  }

  // Upload document endpoint
  fastify.post('/entities/:entityId/documents', async (request, reply) => {
    try {
      const { entityId } = EntityParamsSchema.parse(request.params)
      const uploadRequest = DocumentUploadRequest.parse(request.body)

      // Use user-scoped client for RLS enforcement
      const supabaseClient = request.userSupabase || fastify.supabase

      // Check feature flag
      const featureFlag = await checkFeatureFlag(supabaseClient, entityId, 'InboxEnabled')
      if (!featureFlag.enabled) {
        const response: ApiResponse = {
          success: false,
          error: 'Inbox feature not enabled for this entity'
        }
        return reply.code(403).send(response)
      }

      // Insert document record (file_hash will be computed by worker from actual file content)
      const { data: document, error } = await (supabaseClient as any)
        .from('inbox_documents')
        .insert({
          entity_id: entityId,
          path: uploadRequest.path,
          // file_hash will be set by worker after processing actual file bytes
          mime_type: uploadRequest.mime_type,
          source: uploadRequest.source,
          status: 'uploaded'
        })
        .select('id')
        .single()

      if (error) {
        fastify.log.error(`Failed to insert document: ${error.message}`)
        const response: ApiResponse = {
          success: false,
          error: 'Failed to create document record'
        }
        return reply.code(500).send(response)
      }

      // Generate signed URL for workers
      const { data: signedUrl } = await fastify.supabase.storage
        .from('inbox')
        .createSignedUrl(uploadRequest.path, 3600)

      if (!signedUrl) {
        const response: ApiResponse = {
          success: false,
          error: 'Failed to generate file URL for processing'
        }
        return reply.code(500).send(response)
      }

      // Call workers to process document
      try {
        const workersUrl = process.env.WORKERS_URL || 'http://localhost:8000'
        const workersResponse = await fetch(`${workersUrl}/process-inbox-document`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            document_id: document.id,
            entity_id: entityId,
            file_url: signedUrl.signedUrl
          })
        })

        if (!workersResponse.ok) {
          throw new Error(`Workers responded with status ${workersResponse.status}`)
        }

        const workersData = await workersResponse.json() as { task_id: string }
        fastify.log.info(`Document processing started with task ${workersData.task_id}`)

      } catch (workersError) {
        fastify.log.error(`Failed to start processing: ${workersError}`)
        // Document is created but processing failed - not a fatal error
      }

      const response: ApiResponse = {
        success: true,
        data: {
          document_id: document.id,
          status: 'uploaded',
          message: 'Document uploaded and processing started'
        }
      }

      return response

    } catch (error: any) {
      fastify.log.error(`Upload failed: ${error}`)

      const response: ApiResponse = {
        success: false,
        error: error.message || 'Internal server error'
      }

      return reply.code(error.message?.includes('not enabled') ? 403 : 500).send(response)
    }
  })

  // List documents endpoint
  fastify.get('/entities/:entityId/documents', async (request, reply) => {
    try {
      const { entityId } = EntityParamsSchema.parse(request.params)
      const { status, page, limit } = DocumentListQuerySchema.parse(request.query)

      // Use user-scoped client for RLS enforcement
      const supabaseClient = request.userSupabase || fastify.supabase

      // Check feature flag
      const featureFlag = await checkFeatureFlag(supabaseClient, entityId, 'InboxEnabled')
      if (!featureFlag.enabled) {
        const response: ApiResponse = {
          success: false,
          error: 'Inbox feature not enabled for this entity'
        }
        return reply.code(403).send(response)
      }

      // Build query
      let query = (supabaseClient as any)
        .from('inbox_documents')
        .select(`
          id, entity_id, path, mime_type, source, status, confidence,
          created_at, updated_at, error_msg,
          extraction->supplier->>name as supplier_name,
          extraction->invoice->>number as invoice_number,
          extraction->invoice->>issue_date as invoice_date,
          extraction->invoice->>gross as gross_amount
        `, { count: 'exact' })
        .eq('entity_id', entityId)
        .order('created_at', { ascending: false })

      if (status) {
        query = query.eq('status', status)
      }

      // Apply pagination
      const offset = (page - 1) * limit
      const { data: documents, error, count } = await query.range(offset, offset + limit - 1)

      if (error) {
        fastify.log.error(`Failed to fetch documents: ${error.message}`)
        const response: ApiResponse = {
          success: false,
          error: 'Failed to fetch documents'
        }
        return reply.code(500).send(response)
      }

      const totalPages = count ? Math.ceil(count / limit) : 0

      const response: ApiResponse = {
        success: true,
        data: {
          documents: documents || [],
          pagination: {
            page,
            limit,
            total: count || 0,
            pages: totalPages
          }
        }
      }

      return response

    } catch (error: any) {
      fastify.log.error(`List documents failed: ${error}`)

      const response: ApiResponse = {
        success: false,
        error: error.message || 'Internal server error'
      }

      return reply.code(500).send(response)
    }
  })

  // Get document detail
  fastify.get('/documents/:documentId', async (request, reply) => {
    try {
      const { documentId } = DocumentParamsSchema.parse(request.params)

      // Use user-scoped client for RLS enforcement
      const supabaseClient = request.userSupabase || fastify.supabase

      // Fetch document
      const { data: document, error } = await (supabaseClient as any)
        .from('inbox_documents')
        .select('*')
        .eq('id', documentId)
        .single()

      if (error || !document) {
        const response: ApiResponse = {
          success: false,
          error: 'Document not found'
        }
        return reply.code(404).send(response)
      }

      const response: ApiResponse = {
        success: true,
        data: document
      }

      return response

    } catch (error: any) {
      fastify.log.error(`Get document failed: ${error}`)

      const response: ApiResponse = {
        success: false,
        error: error.message || 'Internal server error'
      }

      return reply.code(500).send(response)
    }
  })

  // Confirm document
  fastify.post('/documents/:documentId/confirm', async (request, reply) => {
    try {
      const { documentId } = DocumentParamsSchema.parse(request.params)
      // Parse confirmation request (currently unused but validates structure)
      ConfirmRequest.parse(request.body)

      // Use user-scoped client for RLS enforcement
      const supabaseClient = request.userSupabase || fastify.supabase

      // Fetch document to verify state and permissions
      const { data: document, error } = await (supabaseClient as any)
        .from('inbox_documents')
        .select('*')
        .eq('id', documentId)
        .single()

      if (error || !document) {
        const response: ApiResponse = {
          success: false,
          error: 'Document not found'
        }
        return reply.code(404).send(response)
      }

      if (!['suggested', 'extracted'].includes(document.status)) {
        const response: ApiResponse = {
          success: false,
          error: `Cannot confirm document in status '${document.status}'`
        }
        return reply.code(400).send(response)
      }

      // Determine entity operating mode to decide between ledger vs assist processing
      const { data: operatingMode, error: operatingModeError } = await (supabaseClient as any)
        .from('operating_modes')
        .select('mode, config')
        .eq('entity_id', document.entity_id)
        .single()

      if (operatingModeError) {
        fastify.log.error(`Failed to fetch operating mode: ${operatingModeError.message}`)
        const response: ApiResponse = {
          success: false,
          error: 'Failed to determine entity mode'
        }
        return reply.code(500).send(response)
      }

      const entityMode = operatingMode?.mode || 'assist' // Default to assist mode
      let finalStatus: string
      let journalId: number | undefined
      let exportRef: string | undefined

      if (entityMode === 'ledger' && document.suggestion) {
        // Ledger mode: Post to journal using RPC
        try {
          const postJournalCall = {
            p_entity: document.entity_id,
            p_type: 'purchase',
            p_description: document.suggestion.description,
            p_date: document.suggestion.journalDate,
            p_lines: document.suggestion.lines.map((line: any) => ({
              account_id: line.accountId,
              debit_amount: parseFloat(line.debit) || null,
              credit_amount: parseFloat(line.credit) || null,
              vat_code_id: line.vatCodeId,
              description: line.memo
            })),
            ...(document.suggestion.reference && { p_reference: document.suggestion.reference })
          }

          const { data: journalResult, error: journalError } = await supabaseClient
            .rpc('rpc_post_journal', postJournalCall)

          if (journalError) {
            throw new Error(`Journal posting failed: ${journalError.message}`)
          }

          journalId = journalResult
          finalStatus = 'posted'

          // Update document with journal reference
          const { error: updateError } = await (supabaseClient as any)
            .from('inbox_documents')
            .update({
              status: finalStatus,
              posted_journal_id: journalId,
              updated_at: new Date().toISOString()
            })
            .eq('id', documentId)

          if (updateError) {
            fastify.log.error(`Failed to update document with journal ID: ${updateError.message}`)
          }

        } catch (journalError: any) {
          fastify.log.error(`Failed to post journal: ${journalError.message}`)
          const response: ApiResponse = {
            success: false,
            error: 'Failed to post journal entry'
          }
          return reply.code(500).send(response)
        }

      } else {
        // Assist mode: Mark for export
        exportRef = `export_${Date.now()}_${documentId}`
        finalStatus = 'exported'

        const { error: updateError } = await (supabaseClient as any)
          .from('inbox_documents')
          .update({
            status: finalStatus,
            export_ref: exportRef,
            updated_at: new Date().toISOString()
          })
          .eq('id', documentId)

        if (updateError) {
          fastify.log.error(`Failed to update document for export: ${updateError.message}`)
          const response: ApiResponse = {
            success: false,
            error: 'Failed to mark document for export'
          }
          return reply.code(500).send(response)
        }
      }

      const response: ApiResponse = {
        success: true,
        data: {
          document_id: documentId,
          status: finalStatus,
          journal_id: journalId,
          export_ref: exportRef,
          message: `Document ${finalStatus === 'posted' ? 'posted to journal' : 'marked for export'} successfully`
        }
      }

      return response

    } catch (error: any) {
      fastify.log.error(`Confirm document failed: ${error}`)

      const response: ApiResponse = {
        success: false,
        error: error.message || 'Internal server error'
      }

      return reply.code(500).send(response)
    }
  })

  // Feature flag check
  fastify.get('/entities/:entityId/feature-flags', async (request, reply) => {
    try {
      const { entityId } = EntityParamsSchema.parse(request.params)
      const featureFlagRequest = FeatureFlagRequest.parse({ entity_id: entityId, flag: (request.query as any).flag })

      // Use user-scoped client for RLS enforcement
      const supabaseClient = request.userSupabase || fastify.supabase

      const featureFlag = await checkFeatureFlag(supabaseClient, entityId, featureFlagRequest.flag)

      const response: ApiResponse = {
        success: true,
        data: featureFlag
      }

      return response

    } catch (error: any) {
      fastify.log.error(`Feature flag check failed: ${error}`)

      const response: ApiResponse = {
        success: false,
        error: error.message || 'Internal server error'
      }

      return reply.code(500).send(response)
    }
  })
}
