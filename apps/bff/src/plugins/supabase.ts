import { createClient, SupabaseClient } from '@supabase/supabase-js'
import { FastifyPluginCallback } from 'fastify'
import fastifyPlugin from 'fastify-plugin'

declare module 'fastify' {
  interface FastifyInstance {
    supabase: SupabaseClient<any>
  }
}

export interface SupabasePluginOptions {
  url: string
  serviceRoleKey: string
}

const supabasePlugin: FastifyPluginCallback<SupabasePluginOptions> = async (fastify, options) => {
  const supabase = createClient(options.url, options.serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })

  fastify.decorate('supabase', supabase)

  fastify.log.info('Supabase client initialized')
}

export default fastifyPlugin(supabasePlugin, {
  name: 'supabase-plugin'
})
