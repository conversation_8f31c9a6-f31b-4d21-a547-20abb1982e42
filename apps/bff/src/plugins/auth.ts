import { FastifyPluginCallback, FastifyRequest } from 'fastify'
import fastifyPlugin from 'fastify-plugin'

export interface AuthPluginOptions {
  internalKey: string
}

declare module 'fastify' {
  interface FastifyRequest {
    isAuthenticated: boolean
  }
}

const authPlugin: FastifyPluginCallback<AuthPluginOptions> = async (fastify, options) => {
  fastify.decorateRequest('isAuthenticated', false)

  fastify.addHook('preHandler', async (request: FastifyRequest, reply) => {
    // Skip auth for health check
    if (request.url === '/healthz') {
      request.isAuthenticated = true
      return
    }

    const internalKey = request.headers['x-internal-key']

    if (!internalKey || internalKey !== options.internalKey) {
      request.isAuthenticated = false
      void reply.status(401).send({
        success: false,
        error: 'Unauthorized: Missing or invalid X-Internal-Key header'
      })
      return
    }

    request.isAuthenticated = true
  })

  fastify.log.info('Auth plugin initialized')
}

export default fastifyPlugin(authPlugin, {
  name: 'auth-plugin'
})
