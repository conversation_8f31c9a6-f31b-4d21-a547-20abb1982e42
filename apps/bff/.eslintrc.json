{"env": {"es2022": true, "node": true}, "extends": ["eslint:recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2022, "sourceType": "module"}, "rules": {"no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "no-var": "error", "prefer-const": "error", "no-trailing-spaces": "error", "eol-last": "error", "comma-dangle": ["error", "never"], "quotes": ["error", "single"], "semi": ["error", "never"]}, "ignorePatterns": ["dist/", "node_modules/"]}