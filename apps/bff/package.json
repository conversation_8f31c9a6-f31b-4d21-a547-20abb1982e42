{"name": "@ledgerly/bff", "version": "0.1.0", "description": "<PERSON><PERSON><PERSON> for Frontend API", "main": "dist/index.js", "type": "module", "scripts": {"dev": "nodemon --exec tsx src/index.ts", "build": "tsc", "start": "node dist/index.js", "typecheck": "tsc --noEmit", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix"}, "dependencies": {"@ledgerly/types": "workspace:*", "@ledgerly/dal": "workspace:*", "fastify": "^4.28.1", "fastify-plugin": "^4.5.1", "@fastify/cors": "^9.0.1", "@fastify/env": "^4.4.0", "@supabase/supabase-js": "^2.45.4", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20.14.10", "@typescript-eslint/eslint-plugin": "^7.16.0", "@typescript-eslint/parser": "^7.16.0", "eslint": "^8.57.0", "nodemon": "^3.1.4", "pino-pretty": "^11.2.1", "tsx": "^4.16.2", "typescript": "^5.5.3"}, "engines": {"node": ">=18.0.0"}}