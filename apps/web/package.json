{"name": "@ledgerly/web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit"}, "dependencies": {"@ledgerly/types": "workspace:*", "@ledgerly/dal": "workspace:*", "@supabase/supabase-js": "^2.38.5", "next": "14.2.15", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@types/node": "^20.10.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "autoprefixer": "^10.4.20", "eslint": "^8.57.1", "eslint-config-next": "14.2.15", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "typescript": "^5.6.3"}}