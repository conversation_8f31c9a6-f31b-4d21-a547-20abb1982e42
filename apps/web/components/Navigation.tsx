'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'

const navigationItems = [
  { name: 'Dashboard', href: '/' as const, icon: '📊' },
  { name: 'Inbox', href: '/inbox' as const, icon: '📥' },
  { name: 'Ledger', href: '/ledger' as const, icon: '📚' },
  { name: 'VAT', href: '/vat' as const, icon: '📄' },
]

export function Navigation() {
  const pathname = usePathname()

  return (
    <nav className="w-64 bg-gray-50 border-r border-gray-200 p-4">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">Ledgerly</h1>
        <p className="text-sm text-gray-600">Accounting Management</p>
      </div>
      
      <div className="space-y-2">
        {navigationItems.map((item) => {
          const isActive = pathname === item.href
          return (
            <Link
              key={item.href}
              href={item.href}
              className={`flex items-center space-x-3 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                isActive
                  ? 'bg-blue-100 text-blue-900 border-blue-200 border'
                  : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
              }`}
            >
              <span className="text-lg">{item.icon}</span>
              <span>{item.name}</span>
            </Link>
          )
        })}
      </div>
    </nav>
  )
}