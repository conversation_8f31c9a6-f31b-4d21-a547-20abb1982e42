export default function VATPage() {
  return (
    <div className="max-w-6xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">VAT Management</h1>
        <p className="text-gray-600">Manage VAT returns, rates, and compliance</p>
      </div>

      {/* Period Selection */}
      <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex gap-2">
          <select className="px-3 py-2 border border-gray-300 rounded-md text-sm">
            <option>Q3 2024 (Jul-Sep)</option>
            <option>Q2 2024 (Apr-Jun)</option>
            <option>Q1 2024 (Jan-Mar)</option>
            <option>Q4 2023 (Oct-Dec)</option>
          </select>
          <select className="px-3 py-2 border border-gray-300 rounded-md text-sm">
            <option>Belgium</option>
            <option>Netherlands</option>
            <option>Germany</option>
          </select>
        </div>
        
        <div className="flex gap-2">
          <button className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 transition-colors">
            Generate Return
          </button>
          <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md text-sm hover:bg-gray-50 transition-colors">
            Export XML
          </button>
        </div>
      </div>

      {/* VAT Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-8">
        {[
          { label: 'VAT Collected', amount: '€8,750.00', description: 'Output VAT on sales', positive: true },
          { label: 'VAT Paid', amount: '€3,420.00', description: 'Input VAT on purchases', positive: false },
          { label: 'Net VAT Due', amount: '€5,330.00', description: 'Amount to pay', positive: false },
          { label: 'Due Date', amount: '20 Oct', description: '2024 Q3 Return', neutral: true }
        ].map((item, index) => (
          <div key={index} className="bg-white rounded-lg shadow p-4 border border-gray-200">
            <p className="text-sm font-medium text-gray-500">{item.label}</p>
            <p className={`text-xl font-bold mt-1 ${
              item.neutral ? 'text-gray-900' : 
              item.positive ? 'text-green-600' : 'text-red-600'
            }`}>
              {item.amount}
            </p>
            <p className="text-xs text-gray-500 mt-1">{item.description}</p>
          </div>
        ))}
      </div>

      {/* VAT Return Details */}
      <div className="grid gap-6 lg:grid-cols-2 mb-8">
        {/* Output VAT */}
        <div className="bg-white rounded-lg shadow border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Output VAT (Sales)</h2>
          </div>
          
          <div className="p-6">
            <div className="space-y-4">
              {[
                { rate: '21%', description: 'Standard rate sales', base: '€35,450.00', vat: '€7,445.00' },
                { rate: '12%', description: 'Reduced rate sales', base: '€8,750.00', vat: '€1,050.00' },
                { rate: '6%', description: 'Low rate sales', base: '€4,250.00', vat: '€255.00' },
                { rate: '0%', description: 'Zero-rated/exempt', base: '€2,150.00', vat: '€0.00' }
              ].map((item, index) => (
                <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                  <div>
                    <p className="text-sm font-medium text-gray-900">{item.rate}</p>
                    <p className="text-xs text-gray-500">{item.description}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-900">Base: {item.base}</p>
                    <p className="text-sm font-medium text-gray-900">VAT: {item.vat}</p>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="flex justify-between">
                <span className="font-medium text-gray-900">Total Output VAT</span>
                <span className="font-bold text-gray-900">€8,750.00</span>
              </div>
            </div>
          </div>
        </div>

        {/* Input VAT */}
        <div className="bg-white rounded-lg shadow border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Input VAT (Purchases)</h2>
          </div>
          
          <div className="p-6">
            <div className="space-y-4">
              {[
                { rate: '21%', description: 'Office supplies & equipment', base: '€12,450.00', vat: '€2,615.00' },
                { rate: '21%', description: 'Professional services', base: '€2,850.00', vat: '€599.00' },
                { rate: '12%', description: 'Books & materials', base: '€850.00', vat: '€102.00' },
                { rate: '6%', description: 'Food & beverages', base: '€1,750.00', vat: '€105.00' }
              ].map((item, index) => (
                <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                  <div>
                    <p className="text-sm font-medium text-gray-900">{item.rate}</p>
                    <p className="text-xs text-gray-500">{item.description}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-900">Base: {item.base}</p>
                    <p className="text-sm font-medium text-gray-900">VAT: {item.vat}</p>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="flex justify-between">
                <span className="font-medium text-gray-900">Total Input VAT</span>
                <span className="font-bold text-gray-900">€3,421.00</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* VAT Return Status */}
      <div className="bg-white rounded-lg shadow border border-gray-200 mb-8">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Return Status & History</h2>
        </div>
        
        <div className="p-6">
          <div className="space-y-4">
            {[
              { 
                period: 'Q3 2024', 
                status: 'Draft', 
                dueDate: '20 Oct 2024', 
                amount: '€5,330.00', 
                statusColor: 'bg-yellow-100 text-yellow-800' 
              },
              { 
                period: 'Q2 2024', 
                status: 'Submitted', 
                dueDate: '20 Jul 2024', 
                amount: '€4,850.00', 
                statusColor: 'bg-green-100 text-green-800' 
              },
              { 
                period: 'Q1 2024', 
                status: 'Paid', 
                dueDate: '20 Apr 2024', 
                amount: '€6,120.00', 
                statusColor: 'bg-blue-100 text-blue-800' 
              }
            ].map((item, index) => (
              <div key={index} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                <div className="flex items-center gap-4">
                  <div>
                    <p className="text-sm font-medium text-gray-900">{item.period}</p>
                    <p className="text-xs text-gray-500">Due: {item.dueDate}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-4">
                  <span className="text-sm font-medium text-gray-900">{item.amount}</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${item.statusColor}`}>
                    {item.status}
                  </span>
                  <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                    View Details
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Current VAT Rates */}
      <div className="bg-white rounded-lg shadow border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Current VAT Rates (Belgium)</h2>
          <p className="text-sm text-gray-500 mt-1">Effective rates for the current period</p>
        </div>
        
        <div className="p-6">
          <div className="grid gap-4 md:grid-cols-2">
            {[
              { rate: '21%', description: 'Standard rate', examples: 'Most goods and services' },
              { rate: '12%', description: 'Reduced rate', examples: 'Restaurant meals, social housing' },
              { rate: '6%', description: 'Reduced rate', examples: 'Food, books, medicines' },
              { rate: '0%', description: 'Zero rate / Exempt', examples: 'Exports, financial services' }
            ].map((item, index) => (
              <div key={index} className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-lg font-bold text-gray-900">{item.rate}</span>
                  <span className="text-sm text-gray-600">{item.description}</span>
                </div>
                <p className="text-xs text-gray-500">{item.examples}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}