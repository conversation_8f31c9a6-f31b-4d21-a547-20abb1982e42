{"root": true, "extends": ["@typescript-eslint/recommended", "plugin:@typescript-eslint/recommended-requiring-type-checking"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "security"], "parserOptions": {"project": "./tsconfig.json", "ecmaVersion": "latest", "sourceType": "module"}, "rules": {"@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/prefer-const": "error", "@typescript-eslint/no-var-requires": "error", "security/detect-object-injection": "warn", "security/detect-non-literal-regexp": "warn", "security/detect-unsafe-regex": "error"}, "ignorePatterns": ["dist/", "build/", "node_modules/", "*.js", "*.mjs"]}